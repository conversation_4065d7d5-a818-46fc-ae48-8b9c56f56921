package web3

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/ecdsa"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"regexp"

	"github.com/btcsuite/btcutil/base58"
	"github.com/ethereum/go-ethereum/crypto"
)

// NewEthWallet generates a new Ethereum wallet and returns the address and private key
func NewEthWallet() (string, string, error) {
	privateKey, err := crypto.GenerateKey()
	if err != nil {
		return "", "", fmt.Errorf("failed to generate private key: %v", err)
	}

	privateKeyBytes := crypto.FromECDSA(privateKey)
	privateKeyHex := fmt.Sprintf("%x", privateKeyBytes)

	address, err := GetAddressFromPrivateKey(privateKeyHex)
	if err != nil {
		return "", "", fmt.Errorf("failed to get address from private key: %v", err)
	}
	return address, privateKeyHex, nil
}

// GetAddressFromPrivate<PERSON>ey calculates the Ethereum wallet address from a given private key
func GetAddressFromPrivateKey(privateKeyHex string) (string, error) {
	privateKey, err := crypto.HexToECDSA(privateKeyHex)
	if err != nil {
		return "", fmt.Errorf("invalid private key: %v", err)
	}

	publicKey := privateKey.Public()
	publicKeyECDSA, ok := publicKey.(*ecdsa.PublicKey)
	if !ok {
		return "", fmt.Errorf("failed to cast public key to ECDSA")
	}

	address := crypto.PubkeyToAddress(*publicKeyECDSA).Hex()

	return address, nil
}

// AES加密（CTR模式）
func AES_CTR_Encrypt(plainText, key, iv []byte) []byte {
	block, err := aes.NewCipher(key)
	if err != nil {
		panic(err)
	}
	blockMode := cipher.NewCTR(block, iv)
	cipherText := make([]byte, len(plainText))
	blockMode.XORKeyStream(cipherText, plainText)
	return cipherText
}

func StringToBuffer(s string, n int) []byte {
	z := []byte(s)
	if n == 0 {
		return z
	}
	l := len(z)
	if n > 0 {
		if l == n {
			return z
		}
		if l > n {
			return z[:n]
		}
		buf := make([]byte, n)
		copy(buf, z)
		return buf
	}
	n = -n
	if l == n {
		return z
	}
	buf := make([]byte, n)
	i := 0
	if l < n {
		copy(buf, z)
		for i = l; i < n; i++ {
			buf[i] = byte(i)
		}
		return buf
	}
	copy(buf, z[:n])
	k := 0
	for i = n; i < l; i++ {
		buf[k] ^= byte(i) ^ z[i]
		k++
		if k == n {
			k = 0
		}
	}
	return buf
}
func StringToKey256_(key_, x_, in_ string, n int) string {
	var key = StringToBuffer(key_, -16)
	var sub = StringToBuffer(x_, -16)
	var text = StringToBuffer(in_, -32)
	k := key
	for i := n; i > 0; i-- {
		k = AES_CTR_Encrypt(key, key, k)
	}
	iv0 := text[:16]
	iv1 := text[16:]
	pri0 := AES_CTR_Encrypt(sub, k, iv0)
	pri1 := AES_CTR_Encrypt(sub, k, iv1)
	hex0 := hex.EncodeToString(pri0)
	hex1 := hex.EncodeToString(pri1)
	hex := hex0 + hex1
	return hex
}

func StringToKey256(key, x, in []byte, n int) string {
	k := key
	for i := 0; i < n; i++ {
		k = AES_CTR_Encrypt(key, key, k)
	}
	text := make([]byte, 32)
	l := len(in)
	if l < 32 {
		var i int
		for i = 0; i < l; i++ {
			text[i] = byte(in[i])
		}
		for i = l; i < 32; i++ {
			text[i] = byte(i)
		}
	} else {
		text = in[:32]
		for i := 32; i < l; i++ {
			text[i&31] ^= byte(i) ^ byte(in[i])
		}
	}
	iv0 := text[:16]
	iv1 := text[16:]
	pri0 := AES_CTR_Encrypt(x, k, iv0)
	pri1 := AES_CTR_Encrypt(x, k, iv1)
	hex0 := hex.EncodeToString(pri0)
	hex1 := hex.EncodeToString(pri1)
	hex := hex0 + hex1
	return hex
}

// 是否为TRON地址
func IsValidTRONAddress(address string) bool {
	regex := regexp.MustCompile("^T[0-9A-HJ-NP-Za-km-z]{33}$")
	return regex.MatchString(address)
}

// 是否为BSC地址
func IsValidBSCAddress(address string) bool {
	// BSC addresses are the same format as Ethereum addresses and start with "0x" followed by 40 hexadecimal characters
	regex := regexp.MustCompile("^0x[0-9a-fA-F]{40}$")
	return regex.MatchString(address)
}

// 是否为BSC私钥
func IsValidBSCPrivateKey(key string) bool {
	// BSC addresses are the same format as Ethereum addresses and start with "0x" followed by 40 hexadecimal characters
	regex := regexp.MustCompile("^[0-9a-fA-F]{64}$")
	return regex.MatchString(key)
}

// GetTRONAddressFromPrivateKey calculates the Tron address from a given private key hex string
func GetTRONAddressFromPrivateKey(privateKeyHex string) (string, error) {
	privKey, err := crypto.HexToECDSA(privateKeyHex)
	if err != nil {
		return "", fmt.Errorf("invalid private key: %v", err)
	}
	pubBytes := crypto.FromECDSAPub(&privKey.PublicKey)
	// Compute Keccak256 hash of public key (skip first byte)
	hash := crypto.Keccak256(pubBytes[1:])
	// Tron address: prefix 0x41 + last 20 bytes of hash
	addr := append([]byte{0x41}, hash[len(hash)-20:]...)
	// Checksum: first 4 bytes of double SHA256
	first := sha256.Sum256(addr)
	second := sha256.Sum256(first[:])
	addrWithChecksum := append(addr, second[:4]...)
	// Encode to Base58
	return base58.Encode(addrWithChecksum), nil
}
