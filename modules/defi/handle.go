package defi

import (
	"s2/common/cache"
	"s2/gsconf"
	"s2/modules/defi/jackpot"
	"s2/pb"
	"time"

	"github.com/jfcwrlight/core/conf"
	"github.com/jfcwrlight/core/conf/table"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/message"
)

func onDefiTryJackpotReq(body *pb.DefiTryJackpotReq, response func(*pb.DefiTryJackpotResp, error)) {
	payout, _ := jackpot.Module.TriggerPayout(body.Debug)
	if body.X > 0 {
		payout.Award = 0
		payout.Prize = ""
	}
	resp := pb.DefiTryJackpotResp{Code: pb.SUCCESS}
	var jp = pb.JackpotSpin{}
	if payout.Award > 0 {
		jp, _ = jackpot.Module.GetSpinDetail(payout)
		jp.Uid = body.Uid
		jp.Url = conf.Str("gameurl.jackpotgame", "")
		jp.EnterJackpot = true
		log.Info("jackpot中奖了GameID:", body.GameID, " SeatID:", body.SeatID, " uid:", body.Uid, " JpAmount:", payout.Award, " 奖品:", payout.Prize)
	} else {
		jp.EnterJackpot = false
	}
	jp.SmallSpin = true
	jp.EnterTimeout = 0
	resp.Jackpot = &jp
	response(&resp, nil)
	//主动推送jackpot
	gameinfo := table.Get[gsconf.GameInfoConf](body.GameID)
	bc, err := cache.QueryUserBasicInfo(body.Uid)
	if err == nil {
		if gameinfo != nil {
			if gameinfo.JackPotOpen == 2 {
				// err = message.Anycast(define.ModuleName.Lobby, &pb.JackpotTriggerToLobbyReq{
				// 	UserID:      body.Uid,
				// 	JackpotInfo: resp.Jackpot,
				// })
				_, err = message.Request[pb.JackpotTriggerToLobbyResp](bc.ServerID, &pb.JackpotTriggerToLobbyReq{
					UserID:      body.Uid,
					JackpotInfo: resp.Jackpot,
					GameID:      body.GameID,
					SeatID:      body.SeatID,
				})
				if err != nil {
					log.Info("JackpotTriggerResp Error:", err)
				}
			}
			if payout.Award > 0 {
				playData := &pb.StatisticsPlayData{
					UserID:   body.Uid,
					GameId:   gameinfo.ID,
					Platfrom: gameinfo.Platform,
					Date:     uint32(time.Now().Unix()),
					SeatId:   body.SeatID,
					Jackpot:  float64(payout.Award),
				}
				message.Stream.Cast(bc.ServerID, &pb.StatisticsReq{
					UserID:   body.Uid,
					PlayData: playData,
				})
			}
		}
	}
}
func onDefiInputReq(body *pb.DefiInputReq) {
	log.Info("defi.InputReq:", body)
	PoolMgr.Cache(body.Info.InputUnite, body.Info.ValidOutput)
}

func onDefiRankReq(body *pb.DefiRankReq, response func(*pb.DefiRankResp, error)) {
	response(&pb.DefiRankResp{Code: pb.SUCCESS}, nil)
}
func onDefiQueryJackpotReq(body *pb.DefiQueryJackpotReq, response func(*pb.DefiQueryJackpotResp, error)) {
	jackpotInfo, err := jackpot.Module.Query()
	if err != nil {
		return
	}
	log.Info(jackpotInfo)
	response(&pb.DefiQueryJackpotResp{UserID: body.UserID, Data: &pb.JackpotNtf{
		Grand:      jackpotInfo.Grand,
		Major:      jackpotInfo.Major,
		Minor:      jackpotInfo.Minor,
		Mini:       jackpotInfo.Mini,
		UpdateTime: jackpotInfo.Time,
	}}, nil)
}
