package account

import (
	"crypto/md5"
	"encoding/base64"
	"fmt"
	"s2/common"
	"s2/common/cache"
	"s2/define"
	"s2/gsconf"
	"s2/pb"
	"slices"
	"sort"
	"strconv"
	"strings"
	"time"

	"s2/mbi"

	"github.com/gin-gonic/gin"
	"github.com/jfcwrlight/core/conf/table"
	"github.com/jfcwrlight/core/infra/chdb"
	"github.com/jfcwrlight/core/infra/mdb"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/message"
	"github.com/jfcwrlight/core/utils/hs"
	"gorm.io/gorm"
)

func (uc *useCase) onAdminLoginReq(ctx *gin.Context, body *pb.AdminLoginReq) {
	var data []*admin
	err := mdb.Default().Table(TableName).Find(&data, "username = ?", body.Username).Error
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	if len(data) == 0 {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "username not found"})
		return
	}
	sum := md5.Sum([]byte(body.Password))
	hash := base64.StdEncoding.EncodeToString(sum[:])
	if hash != data[0].PwdDigest {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "password error"})
		return
	}
	token := common.GenToken()
	uc.tokenCache.Store(token, body.Username)
	uc.tokenAppIdCache.Store(token, data[0].AppID)
	ctx.JSON(200, pb.AdminLoginResp{
		Code:       pb.SUCCESS,
		Token:      token,
		Username:   body.Username,
		AppID:      data[0].AppID,
		Permission: strings.Split(data[0].Permission, ","),
	})
}

func (uc *useCase) onAdminAccountListReq(ctx *gin.Context, _ *pb.AdminAccountListReq) {
	var data []*admin
	err := mdb.Default().Table(TableName).Find(&data).Error
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	if len(data) == 0 {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "username not found"})
		return
	}
	list := make([]*pb.AdminAccount, 0, len(data))
	for _, item := range data {
		list = append(list, item.ToPB())
	}
	ctx.JSON(200, pb.AdminAccountListResp{
		Code: pb.SUCCESS,
		List: list,
	})
}

func (uc *useCase) onAdminAddAccountReq(ctx *gin.Context, body *pb.AdminAddAccountReq) {
	var data []*admin
	err := mdb.Default().Table(TableName).Find(&data, "username = ?", body.Username).Error
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	if len(data) != 0 {
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR, Msg: "username exist"})
		return
	}
	if len(body.Password) == 0 {
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR, Msg: "password empty"})
		return
	}
	sum := md5.Sum([]byte(body.Password))
	hash := base64.StdEncoding.EncodeToString(sum[:])

	appID := body.AppID
	if appID != "" {
		resp, err := message.RequestAny[pb.GetThirdAppToThirdResp](define.ModuleName.ThirdApp, &pb.GetThirdAppToThirdReq{})
		if err != nil {
			log.Error(err)
			hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
			return
		}
		// 最简洁的存在性检查
		if !slices.ContainsFunc(resp.AppList, func(app *pb.ThirdApp) bool {
			return app.AppID == appID
		}) {
			hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "Third app not found"})
			return
		}
	}

	mdb.Default().Table(TableName).Create(admin{Username: body.Username, PwdDigest: hash, Permission: strings.Join(body.Permission, ","), AppID: body.AppID})
	ctx.JSON(200, &pb.AdminAddAccountResp{
		Code: pb.SUCCESS,
	})
}

func (uc *useCase) onAdminUpdateAccountReq(ctx *gin.Context, body *pb.AdminUpdateAccountReq) {
	var data []*admin
	err := mdb.Default().Table(TableName).Find(&data, "username = ?", body.Username).Error
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	if len(data) == 0 {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "username not found"})
		return
	}
	var hash string
	if len(body.Password) != 0 {
		sum := md5.Sum([]byte(body.Password))
		hash = base64.StdEncoding.EncodeToString(sum[:])
	}
	appID := body.AppID
	if appID != "" {
		resp, err := message.RequestAny[pb.GetThirdAppToThirdResp](define.ModuleName.ThirdApp, &pb.GetThirdAppToThirdReq{})
		if err != nil {
			log.Error(err)
			hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
			return
		}
		// 最简洁的存在性检查
		if !slices.ContainsFunc(resp.AppList, func(app *pb.ThirdApp) bool {
			return app.AppID == appID
		}) {
			hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "Third app not found"})
			return
		}
	}
	mdb.Default().Table(TableName).Updates(admin{Username: body.Username, PwdDigest: hash, Permission: strings.Join(body.Permission, ","), AppID: appID})
	hs.OK(ctx, pb.AdminUpdateAccountResp{
		Code: pb.SUCCESS,
	})
}

func (uc *useCase) onAdminUpdatePwdReq(ctx *gin.Context, body *pb.AdminUpdatePwdReq) {
	token := ctx.Request.Header.Get("Token")
	value, ok := uc.tokenCache.Load(token)
	if !ok {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "token not found"})
		return
	}
	username := value.(string)
	if username != body.Username {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: ""})
		return
	}
	if len(body.Password) == 0 {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "username password empty"})
		return
	}
	sum := md5.Sum([]byte(body.Password))
	hash := base64.StdEncoding.EncodeToString(sum[:])
	err := mdb.Default().Table(TableName).Where("username = ?", username).Update("pwd_digest", hash).Error
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	hs.OK(ctx, pb.AdminUpdateAccountResp{
		Code: pb.SUCCESS,
	})
}

func (uc *useCase) onAdminDeleteAccountReq(ctx *gin.Context, body *pb.AdminDeleteAccountReq) {
	err := mdb.Default().Table(TableName).Delete(&admin{Username: body.Username}).Error
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	hs.OK(ctx, &pb.AdminUpdateAccountResp{
		Code: pb.SUCCESS,
	})
}

// onGMUserInputHistoryReq handles querying user spin history for admin
func (uc *useCase) onGMUserInputHistoryReq(ctx *gin.Context, body *pb.GMUserInputHistoryReq) {
	channel, err := uc.GetAppIDByToken(ctx.Request.Header.Get("Token"))
	if err != nil {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: err.Error()})
		return
	}
	if channel != "" {
		body.Channel = channel
	}
	// build dynamic filters and query
	baseCount := chdb.Default().Table(mbi.TableSpinResult)
	baseQuery := chdb.Default().Table(mbi.TableSpinResult).
		Select("private", "user_id", "block_hash", "asset_id", "input", "output", "sn", "create_at", "balance", "mode", "platfrom", "channel", "seat_id", "parent_id", "game_id")
	if body.StartTime == 0 || body.EndTime == 0 {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "start time or end time is empty"})
		return
	}
	if body.StartTime != 0 {
		baseCount = baseCount.Where("create_at >= ?", body.StartTime)
		baseQuery = baseQuery.Where("create_at >= ?", body.StartTime)
	}
	if body.EndTime != 0 {
		baseCount = baseCount.Where("create_at <= ?", body.EndTime)
		baseQuery = baseQuery.Where("create_at <= ?", body.EndTime)
	}
	if body.UserID != 0 {
		baseCount = baseCount.Where("user_id = ?", body.UserID)
		baseQuery = baseQuery.Where("user_id = ?", body.UserID)
	}
	if body.GameID != 0 {
		gID := strconv.Itoa(int(body.GameID))
		baseCount = baseCount.Where("game_id = ?", gID)
		baseQuery = baseQuery.Where("game_id = ?", gID)
	}
	if body.AssetID != 0 {
		assetID := int64(body.AssetID)
		baseCount = baseCount.Where("asset_id = ?", assetID)
		baseQuery = baseQuery.Where("asset_id = ?", assetID)
	}
	if body.ParentID != 0 {
		baseCount = baseCount.Where("parent_id = ?", body.ParentID)
		baseQuery = baseQuery.Where("parent_id = ?", body.ParentID)
	}
	if body.Channel != "" {
		baseCount = baseCount.Where("channel = ?", body.Channel)
		baseQuery = baseQuery.Where("channel = ?", body.Channel)
	}
	// count total records
	var total int64
	err = baseCount.Count(&total).Error
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	// query paginated records
	query := baseQuery.
		Order("create_at DESC").
		Offset(int((body.PageNumber - 1) * body.PageSize)).
		Limit(int(body.PageSize))
	// execute
	var dbRecs []struct {
		Private   string
		UserID    string       `gorm:"user_id"`
		BlockHash string       `gorm:"block_hash"`
		AssetID   pb.EnumAsset `gorm:"asset_id"`
		SN        int32        `gorm:"sn"`
		Input     float64      `gorm:"input"`
		Output    float64      `gorm:"output"`
		Balance   float64      `gorm:"balance"`
		Mode      int32        `gorm:"mode"`
		Platform  string       `gorm:"Platfrom"`
		Channel   string       `gorm:"channel"`
		SeatID    int64        `gorm:"seat_id"`
		ParentID  int64        `gorm:"parent_id"`
		GameID    int32        `gorm:"game_id"`
		CreateAt  int64        `gorm:"create_at"`
	}
	err = query.Find(&dbRecs).Error
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	// prepare response records
	respRecs := make([]*pb.GMGameSpinRecord, len(dbRecs))
	for i, r := range dbRecs {
		gameName := ""
		game := table.Get[gsconf.GameInfoConf](int32(r.GameID))
		if game != nil {
			gameName = game.Name
		}
		respRecs[i] = &pb.GMGameSpinRecord{
			Private:   r.Private,
			BlockHash: r.BlockHash,
			AssetID:   r.AssetID,
			GameSN:    r.SN,
			Input:     r.Input,
			Output:    r.Output,
			Balance:   r.Balance,
			Mode:      r.Mode,
			Time:      r.CreateAt,
			Platform:  r.Platform,
			Channel:   r.Channel,
			SeatID:    r.SeatID,
			ParentID:  r.ParentID,
			GameID:    r.GameID,
			UserId:    r.UserID,
			AssetName: pb.EnumAsset_name[int32(r.AssetID)],
			GameName:  gameName,
		}
	}
	// return
	hs.OK(ctx, pb.GMUserInputHistoryResp{
		Code:    pb.SUCCESS,
		Records: respRecs,
		Total:   total,
	})
}

// onGMAssetHistoryReq handles querying user asset change history for admin
func (uc *useCase) onGMAssetHistoryReq(ctx *gin.Context, body *pb.GMAssetHistoryReq) {
	channel, err := uc.GetAppIDByToken(ctx.Request.Header.Get("Token"))
	if err != nil {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: err.Error()})
		return
	}
	if channel != "" {
		body.Channel = channel
	}
	// build dynamic filters and query
	baseCount := chdb.Default().Table(mbi.TableUserAssetChange)
	baseQuery := chdb.Default().Table(mbi.TableUserAssetChange).
		Select("event_id", "user_id", "change", "balance", "cause", "channel", "parent_id", "asset_id", "create_at")
	if body.StartTime == 0 {
		body.StartTime = uint64(time.Now().Unix() - 3600*24*30)
	}
	if body.EndTime == 0 {
		body.EndTime = uint64(time.Now().Unix())
	}
	if body.StartTime != 0 {
		baseCount = baseCount.Where("create_at >= ?", body.StartTime)
		baseQuery = baseQuery.Where("create_at >= ?", body.StartTime)
	}
	if body.EndTime != 0 {
		baseCount = baseCount.Where("create_at <= ?", body.EndTime)
		baseQuery = baseQuery.Where("create_at <= ?", body.EndTime)
	}
	if body.UserID != 0 {
		baseCount = baseCount.Where("user_id = ?", body.UserID)
		baseQuery = baseQuery.Where("user_id = ?", body.UserID)
	}
	if body.AssetID != 0 {
		assetID := int64(body.AssetID)
		baseCount = baseCount.Where("asset_id = ?", assetID)
		baseQuery = baseQuery.Where("asset_id = ?", assetID)
	}
	if body.ParentID != 0 {
		baseCount = baseCount.Where("parent_id = ?", body.ParentID)
		baseQuery = baseQuery.Where("parent_id = ?", body.ParentID)
	}
	if body.Channel != "" {
		baseCount = baseCount.Where("channel = ?", body.Channel)
		baseQuery = baseQuery.Where("channel = ?", body.Channel)
	}
	// count total records
	var total int64
	err = baseCount.Count(&total).Error
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	// query paginated records
	query := baseQuery.
		Order("create_at DESC").
		Offset(int((body.PageNumber - 1) * body.PageSize)).
		Limit(int(body.PageSize))
	var dbRecs []struct {
		EventID   uint64
		UserID    int64
		Change    float64
		Balance   float64
		Cause     string
		Channel   string
		ParentID  int64
		AssetID   pb.EnumAsset
		CreatedAt int64 `gorm:"column:create_at"`
	}
	err = query.Find(&dbRecs).Error
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	// prepare response records
	respRecs := make([]*pb.GMAssetHistoryRecord, len(dbRecs))
	for i, r := range dbRecs {
		respRecs[i] = &pb.GMAssetHistoryRecord{
			EventID:  r.EventID,
			UserID:   r.UserID,
			Change:   r.Change,
			Balance:  r.Balance,
			Cause:    r.Cause,
			Channel:  r.Channel,
			ParentID: r.ParentID,
			AssetID:  r.AssetID,
			Time:     r.CreatedAt,
		}
	}
	// return
	hs.OK(ctx, pb.GMAssetHistoryResp{
		Code:    pb.SUCCESS,
		Records: respRecs,
		Total:   total,
	})
}

func (uc *useCase) onGMHistoryWithdrawOrderReq(ctx *gin.Context, body *pb.GMHistoryWithdrawOrderReq) {
	channel, err := uc.GetAppIDByToken(ctx.Request.Header.Get("Token"))
	if err != nil {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: err.Error()})
		return
	}
	if channel != "" {
		body.Channel = channel
	}
	resp, err := message.RequestAny[pb.HistoryWithdrawOrderResp](define.ModuleName.Order, &pb.HistoryWithdrawOrderReq{
		PageSize:   int32(body.PageSize),
		PageNumber: int32(body.PageNumber),
		StartTime:  body.StartTime,
		EndTime:    body.EndTime,
		Type:       body.Type,
		Channel:    body.Channel,
	})
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	hs.OK(ctx, pb.GMHistoryWithdrawOrderResp{
		Code:   pb.SUCCESS,
		Orders: resp.Orders,
		Total:  resp.Total,
	})
}

func (uc *useCase) onGMHistoryRechargeOrderReq(ctx *gin.Context, body *pb.GMHistoryRechargeOrderReq) {
	channel, err := uc.GetAppIDByToken(ctx.Request.Header.Get("Token"))
	if err != nil {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: err.Error()})
		return
	}
	if channel != "" {
		body.Channel = channel
	}
	resp, err := message.RequestAny[pb.HistoryRechargeOrderResp](define.ModuleName.Order, &pb.HistoryRechargeOrderReq{
		PageSize:   int32(body.PageSize),
		PageNumber: int32(body.PageNumber),
		StartTime:  body.StartTime,
		EndTime:    body.EndTime,
		Type:       body.Type,
		Channel:    body.Channel,
	})
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	hs.OK(ctx, pb.GMHistoryRechargeOrderResp{
		Code:   pb.SUCCESS,
		Orders: resp.Orders,
		Total:  resp.Total,
	})
}

func (uc *useCase) onGMWithdrawOrderOperationReq(ctx *gin.Context, body *pb.GMWithdrawOrderOperationReq) {
	channel, err := uc.GetAppIDByToken(ctx.Request.Header.Get("Token"))
	if err != nil {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: err.Error()})
		return
	}
	_, err = message.RequestAny[pb.WithdrawOrderOperationResp](define.ModuleName.Order, &pb.WithdrawOrderOperationReq{
		ID:        body.OrderID,
		Operation: body.Operation,
		Channel:   channel,
	})
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	hs.OK(ctx, pb.GMWithdrawOrderOperationResp{
		Code: pb.SUCCESS,
	})
}

func (uc *useCase) onGMChangeAssetReq(ctx *gin.Context, body *pb.GMChangeAssetReq) {
	channel, err := uc.GetAppIDByToken(ctx.Request.Header.Get("Token"))
	if err != nil {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: err.Error()})
		return
	}
	if channel != "" {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "channel not allowed"})
		return
	}
	lobbyinfo := table.Get[gsconf.LobbyInfoConf](1)
	assetID := -1
	for _, item := range lobbyinfo.CurrencyList {
		if item == int32(body.Data.ID) {
			assetID = int(item)
		}
	}
	if assetID == -1 {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "asset id not found"})
		return
	}
	bc, err := cache.QueryUserBasicInfo(body.UserID)
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	resp, err := message.Request[pb.AssetBalanceResp](bc.ServerID, &pb.AssetBalanceReq{
		UserID: body.UserID,
	})
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	balance := resp.Balance[int32(body.Data.ID)]
	var update float64
	if body.Op == 1 {
		update = body.Data.Value - balance
	} else {
		update = body.Data.Value
	}
	changeAssets := []*pb.IDValFloat{
		&pb.IDValFloat{ID: int64(body.Data.ID), Value: update, LeftInput: 0},
	}
	_, err = message.Request[pb.ChangeAssetResp](bc.ServerID, &pb.ChangeAssetReq{
		UserID: body.UserID,
		Cause:  "GM_ChangeAsset",
		Assets: changeAssets,
	})
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	hs.OK(ctx, pb.GMChangeAssetResp{
		Code: pb.SUCCESS,
	})
}

func (uc *useCase) onGMChangeLeftInputReq(ctx *gin.Context, body *pb.GMChangeLeftInputReq) {
	channel, err := uc.GetAppIDByToken(ctx.Request.Header.Get("Token"))
	if err != nil {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: err.Error()})
		return
	}
	if channel != "" {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "channel not allowed"})
		return
	}
	lobbyinfo := table.Get[gsconf.LobbyInfoConf](1)
	assetID := -1
	for _, item := range lobbyinfo.CurrencyList {
		if item == int32(body.Data.ID) {
			assetID = int(item)
		}
	}
	if assetID == -1 {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "asset id not found"})
		return
	}
	bc, err := cache.QueryUserBasicInfo(body.UserID)
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	resp, err := message.Request[pb.AssetResp](bc.ServerID, &pb.AssetReq{
		UserID: body.UserID,
	})
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	asset := resp.Assets[int32(body.Data.ID)]
	var update float64
	if body.Op == 1 {
		update = body.Data.LeftInput - asset.LeftInput
		if body.Data.LeftInput < 0 {
			hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "left input < 0"})
			return
		}
	} else {
		update = body.Data.LeftInput
		if body.Data.LeftInput+asset.LeftInput < 0 {
			body.Data.LeftInput = asset.LeftInput
		}
	}
	changeAssets := []*pb.IDValFloat{
		&pb.IDValFloat{ID: int64(body.Data.ID), Value: 0, LeftInput: update},
	}
	_, err = message.Request[pb.ChangeAssetResp](bc.ServerID, &pb.ChangeAssetReq{
		UserID: body.UserID,
		Cause:  "GM_ChangeLeftInput",
		Assets: changeAssets,
	})
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	hs.OK(ctx, pb.GMChangeLeftInputResp{
		Code: pb.SUCCESS,
	})
}

func (uc *useCase) onGMPlatConfogReq(ctx *gin.Context, body *pb.GMPlatConfogReq) {
	gameList := []*pb.GameConfig{}
	l := table.GetALL[gsconf.GameInfoConf]()
	sort.Slice(l, func(i, j int) bool {
		return l[i].Sort < l[j].Sort
	})
	gameList = append(gameList, &pb.GameConfig{
		GameID:   0,
		GameName: "All",
		Platform: "",
	})
	for _, item := range l {
		gameList = append(gameList, &pb.GameConfig{
			GameID:   item.ID,
			GameName: item.Name,
			Platform: item.Platform,
		})
	}
	lobbyinfo := table.Get[gsconf.LobbyInfoConf](1)
	assetList := []*pb.AssetConfig{}
	for _, item := range lobbyinfo.CurrencyList {
		assetList = append(assetList, &pb.AssetConfig{
			AssetID:   pb.EnumAsset(item),
			AssetName: pb.EnumAsset_name[int32(item)],
		})
	}
	hs.OK(ctx, pb.GMPlatConfogResp{
		Code:      pb.SUCCESS,
		GameList:  gameList,
		AssetList: assetList,
	})
}

func (uc *useCase) onGMSetupGameOddsReq(ctx *gin.Context, body *pb.GMSetupGameOddsReq) {
	channel, err := uc.GetAppIDByToken(ctx.Request.Header.Get("Token"))
	if err != nil {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: err.Error()})
		return
	}
	if channel != "" {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "channel not allowed"})
		return
	}
	resp, err := message.RequestAny[pb.GMSetupOddsToGameResp](define.ModuleName.Game, &pb.GMSetupOddsToGameReq{
		Data: body.Data,
	})
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	gameList := []*pb.GameConfig{}
	l := table.GetALL[gsconf.GameInfoConf]()
	sort.Slice(l, func(i, j int) bool {
		return l[i].Sort < l[j].Sort
	})
	gameList = append(gameList, &pb.GameConfig{
		GameID:   0,
		GameName: "All",
		Platform: "",
	})
	for _, item := range l {
		if item.PlatformS == "x" {
			gameList = append(gameList, &pb.GameConfig{
				GameID:   item.GetID(),
				GameName: item.Name,
				Platform: item.Platform,
			})
		}
	}
	list := []*pb.GMGameOdds{}
	for _, game := range gameList {
		item := &pb.GMGameOdds{
			GameID:   game.GameID,
			GameName: game.GameName,
			Value:    0,
		}
		for _, item2 := range resp.List {
			if item2.GameID == game.GameID {
				item.Value = item2.Value
				break
			}
		}
		list = append(list, item)
	}
	hs.OK(ctx, pb.GMSetupGameOddsResp{
		Code: resp.Code,
		List: list,
	})
}

func (uc *useCase) onGMUserBaseInfoReq(ctx *gin.Context, body *pb.GMUserBaseInfoReq) {
	channel, err := uc.GetAppIDByToken(ctx.Request.Header.Get("Token"))
	if err != nil {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: err.Error()})
		return
	}
	bc, err := cache.QueryUserBasicInfo(body.UserID)
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	respAccount, err := message.RequestAny[pb.GMUserBaseInfoToAccountResp](define.ModuleName.Account, &pb.GMUserBaseInfoToAccountReq{
		UserID: body.UserID,
	})
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	if channel != "" {
		if respAccount.Data.Channel != channel {
			hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "channel not allowed"})
			return
		}
	}
	respLobby, err := message.Request[pb.GMUserBaseInfoToLobbyResp](bc.ServerID, &pb.GMUserBaseInfoToLobbyReq{
		UserID: body.UserID,
	})
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	hs.OK(ctx, pb.GMUserBaseInfoResp{
		Code: pb.SUCCESS,
		Data: &pb.GMUserBaseInfo{
			UserID:           respAccount.Data.UserID,
			RegisterPlatform: respAccount.Data.RegisterPlatform,
			Address:          respAccount.Data.Address,
			ParentUid:        respAccount.Data.ParentUid,
			Channel:          respAccount.Data.Channel,
			CreateTime:       respAccount.Data.CreateTime,
			RegisterIP:       respAccount.Data.RegisterIP,
			RegisterDevice:   respAccount.Data.RegisterDevice,

			Name:          respLobby.Data.Name,
			LoginTime:     respLobby.Data.LoginTime,
			LoginIP:       respLobby.Data.LoginIP,
			LoginDevice:   respLobby.Data.LoginDevice,
			Assets:        respLobby.Data.Assets,
			TotalInput:    respLobby.Data.TotalInput,
			TotalOutput:   respLobby.Data.TotalOutput,
			TotalRecharge: respLobby.Data.TotalRecharge,
			TotalWithdraw: respLobby.Data.TotalWithdraw,
			RechargeCount: respLobby.Data.RechargeCount,
			WithdrawCount: respLobby.Data.WithdrawCount,
		}})
}

func (uc *useCase) onGMAddThirdAppToThirdReq(ctx *gin.Context, body *pb.GMAddThirdAppToThirdReq) {
	resp, err := message.RequestAny[pb.AddThirdAppToThirdResp](define.ModuleName.ThirdApp, &pb.AddThirdAppToThirdReq{
		AppID:       body.AppID,
		Open:        body.Open,
		Name:        body.Name,
		IPWhiteList: body.IPWhiteList,
	})
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	hs.OK(ctx, pb.GMAddThirdAppToThirdResp{
		Code: resp.Code,
	})
}

func (uc *useCase) onGMUpdateThirdAppToThirdReq(ctx *gin.Context, body *pb.GMUpdateThirdAppToThirdReq) {
	if body.AppID == "" {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "app id empty"})
		return
	}
	resp, err := message.RequestAny[pb.UpdateThirdAppToThirdResp](define.ModuleName.ThirdApp, &pb.UpdateThirdAppToThirdReq{
		AppID:       body.AppID,
		EncryptKey:  body.EncryptKey,
		EncryptIV:   body.EncryptIV,
		Open:        body.Open,
		Name:        body.Name,
		IPWhiteList: body.IPWhiteList,
	})
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	hs.OK(ctx, pb.GMUpdateThirdAppToThirdResp{
		Code: resp.Code,
	})
}

func (uc *useCase) onGMGetThirdAppToThirdReq(ctx *gin.Context, body *pb.GMGetThirdAppToThirdReq) {
	resp, err := message.RequestAny[pb.GetThirdAppToThirdResp](define.ModuleName.ThirdApp, &pb.GetThirdAppToThirdReq{})
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	// 将 "All" 选项插入到数组第一个位置
	allOption := &pb.ThirdApp{
		AppID: "",
		Name:  "All",
		Open:  true,
	}
	resp.AppList = append([]*pb.ThirdApp{allOption}, resp.AppList...)
	hs.OK(ctx, pb.GMGetThirdAppToThirdResp{
		Code:    resp.Code,
		AppList: resp.AppList,
	})
}

func (uc *useCase) onGMStatisticsGameDataReq(ctx *gin.Context, body *pb.GMStatisticsGameDataReq) {
	channel, err := uc.GetAppIDByToken(ctx.Request.Header.Get("Token"))
	if err != nil {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: err.Error()})
		return
	}
	if channel != "" {
		body.Channel = channel
	}
	resp, err := message.RequestAny[pb.GMStatisticsGameDataResp](define.ModuleName.Statistics, &pb.StatisticsGameDataReq{
		StartDate: body.StartDate,
		EndDate:   body.EndDate,
		AssetID:   body.AssetID,
		Channel:   body.Channel,
	})
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	hs.OK(ctx, resp)
}

func (uc *useCase) onOperationalStatisticsReq(ctx *gin.Context, body *pb.GMOperationalStatisticsReq) {
	channel, err := uc.GetAppIDByToken(ctx.Request.Header.Get("Token"))
	if err != nil {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: err.Error()})
		return
	}
	if channel != "" {
		body.Channel = channel
	}
	resp, err := message.RequestAny[pb.GMOperationalStatisticsResp](define.ModuleName.Statistics, &pb.OperationalStatisticsReq{
		StartDate: body.StartDate,
		EndDate:   body.EndDate,
		Channel:   body.Channel,
	})
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	hs.OK(ctx, resp)
}

// onGMStatisticsRetentionDataReq 处理留存数据统计请求
func (uc *useCase) onGMStatisticsRetentionDataReq(ctx *gin.Context, body *pb.GMStatisticsRetentionDataReq) {
	channel, err := uc.GetAppIDByToken(ctx.Request.Header.Get("Token"))
	if err != nil {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: err.Error()})
		return
	}
	if channel != "" {
		body.Channel = channel
	}
	log.Debugf("Processing retention statistics request: StartDate=%s, EndDate=%s, Channel=%s",
		body.StartDate, body.EndDate, body.Channel)

	// 解析日期范围
	startDate, endDate, err := common.ParseStatisticsDateRange(body.StartDate, body.EndDate)
	if err != nil {
		log.Errorf("Failed to parse date range: %v", err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}

	// 获取留存统计数据
	retentionData, err := GetRetentionStatistics(startDate, endDate, body.Channel)
	if err != nil {
		log.Errorf("Failed to get retention statistics: %v", err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}

	// 转换为protobuf格式
	var retentionList []*pb.StatisticsRetentionData
	for _, data := range retentionData {
		retentionStats := &pb.StatisticsRetentionData{
			Date:                    data.Date,
			Channel:                 data.Channel,
			NewRegisterCount:        data.NewRegisterCount,
			NextDayRetentionRate:    data.NextDayRetentionRate,
			ThreeDayRetentionRate:   data.ThreeDayRetentionRate,
			SevenDayRetentionRate:   data.SevenDayRetentionRate,
			FifteenDayRetentionRate: data.FifteenDayRetentionRate,
			ThirtyDayRetentionRate:  data.ThirtyDayRetentionRate,
			SixtyDayRetentionRate:   data.SixtyDayRetentionRate,
			NinetyDayRetentionRate:  data.NinetyDayRetentionRate,
		}
		retentionList = append(retentionList, retentionStats)
	}

	resp := &pb.GMStatisticsRetentionDataResp{
		Code: pb.SUCCESS,
		List: retentionList,
	}
	hs.OK(ctx, resp)
}

// RetentionStatisticsData 留存统计数据结构
type RetentionStatisticsData struct {
	Date             string // 日期 YYYY-MM-DD
	Channel          string // 渠道
	NewRegisterCount int32  // 当日新注册用户数

	// 留存用户数 - Date注册的用户在Date+n日登录的用户数
	NextDayRetention    int32 // 次日留存用户数
	ThreeDayRetention   int32 // 3日留存用户数
	SevenDayRetention   int32 // 7日留存用户数
	FifteenDayRetention int32 // 15日留存用户数
	ThirtyDayRetention  int32 // 30日留存用户数
	SixtyDayRetention   int32 // 60日留存用户数
	NinetyDayRetention  int32 // 90日留存用户数

	// 留存率（百分比）
	NextDayRetentionRate    float64 // 次日留存率
	ThreeDayRetentionRate   float64 // 3日留存率
	SevenDayRetentionRate   float64 // 7日留存率
	FifteenDayRetentionRate float64 // 15日留存率
	ThirtyDayRetentionRate  float64 // 30日留存率
	SixtyDayRetentionRate   float64 // 60日留存率
	NinetyDayRetentionRate  float64 // 90日留存率
}

// GetRetentionStatistics 获取留存统计数据
func GetRetentionStatistics(startDate, endDate int32, channel string) ([]*RetentionStatisticsData, error) {
	var results []*RetentionStatisticsData

	// 按日期范围循环处理每一天
	for date := startDate; date <= endDate; date++ {
		dateStr := fmt.Sprintf("%d", date)
		if len(dateStr) == 8 {
			dateStr = fmt.Sprintf("%s-%s-%s", dateStr[:4], dateStr[4:6], dateStr[6:8])
		}

		// 计算该日期的留存数据
		retentionData, err := calculateDayRetention(date, channel)
		if err != nil {
			log.Errorf("Failed to calculate retention for date %d: %v", date, err)
			continue
		}

		if retentionData != nil {
			retentionData.Date = dateStr
			results = append(results, retentionData)
		}
	}

	return results, nil
}

// calculateDayRetention 计算指定日期的留存数据
func calculateDayRetention(registerDate int32, channel string) (*RetentionStatisticsData, error) {
	// 获取当日注册用户
	registerUsers, err := getRegisterUsersByDate(registerDate, channel)
	if err != nil {
		return nil, fmt.Errorf("failed to get register users: %w", err)
	}

	if len(registerUsers) == 0 {
		return nil, nil // 当日无注册用户，跳过
	}

	retentionData := &RetentionStatisticsData{
		Channel:          channel,
		NewRegisterCount: int32(len(registerUsers)),
	}

	// 计算各天留存
	retentionDays := []int{1, 3, 7, 15, 30, 60, 90}
	retentionCounts := make([]int32, len(retentionDays))

	for i, days := range retentionDays {
		targetDate := addDays(registerDate, days)
		loginUsers, err := getLoginUsersByDate(targetDate, channel, registerUsers)
		if err != nil {
			log.Errorf("Failed to get login users for date %d: %v", targetDate, err)
			continue
		}
		retentionCounts[i] = int32(len(loginUsers))
	}

	// 设置留存用户数
	retentionData.NextDayRetention = retentionCounts[0]
	retentionData.ThreeDayRetention = retentionCounts[1]
	retentionData.SevenDayRetention = retentionCounts[2]
	retentionData.FifteenDayRetention = retentionCounts[3]
	retentionData.ThirtyDayRetention = retentionCounts[4]
	retentionData.SixtyDayRetention = retentionCounts[5]
	retentionData.NinetyDayRetention = retentionCounts[6]

	// 计算留存率
	totalRegisters := float64(retentionData.NewRegisterCount)
	if totalRegisters > 0 {
		retentionData.NextDayRetentionRate = float64(retentionData.NextDayRetention) / totalRegisters * 100
		retentionData.ThreeDayRetentionRate = float64(retentionData.ThreeDayRetention) / totalRegisters * 100
		retentionData.SevenDayRetentionRate = float64(retentionData.SevenDayRetention) / totalRegisters * 100
		retentionData.FifteenDayRetentionRate = float64(retentionData.FifteenDayRetention) / totalRegisters * 100
		retentionData.ThirtyDayRetentionRate = float64(retentionData.ThirtyDayRetention) / totalRegisters * 100
		retentionData.SixtyDayRetentionRate = float64(retentionData.SixtyDayRetention) / totalRegisters * 100
		retentionData.NinetyDayRetentionRate = float64(retentionData.NinetyDayRetention) / totalRegisters * 100
	}

	log.Debugf("Calculated retention for date %d, channel %s: register=%d, 1d=%d(%.2f%%), 7d=%d(%.2f%%)",
		registerDate, channel, retentionData.NewRegisterCount,
		retentionData.NextDayRetention, retentionData.NextDayRetentionRate,
		retentionData.SevenDayRetention, retentionData.SevenDayRetentionRate)

	return retentionData, nil
}

// getRegisterUsersByDate 获取指定日期注册的用户ID列表
func getRegisterUsersByDate(date int32, channel string) ([]int64, error) {
	var users []int64

	query := `SELECT user_id FROM user_register WHERE date = ?`
	args := []interface{}{date}

	if channel != "" {
		query += ` AND channel = ?`
		args = append(args, channel)
	}

	rows, err := chdb.Default().Table(mbi.TableUserRegister).Raw(query, args...).Rows()
	if err != nil {
		return nil, fmt.Errorf("failed to query register users: %w", err)
	}
	defer rows.Close()

	for rows.Next() {
		var userID int64
		if err := rows.Scan(&userID); err != nil {
			log.Errorf("Failed to scan user ID: %v", err)
			continue
		}
		users = append(users, userID)
	}

	return users, nil
}

// getLoginUsersByDate 获取指定日期登录的用户ID列表（需要在注册用户范围内）
func getLoginUsersByDate(date int32, channel string, registerUsers []int64) ([]int64, error) {
	if len(registerUsers) == 0 {
		return []int64{}, nil
	}

	var users []int64

	// 构建用户ID占位符
	placeholders := make([]string, len(registerUsers))
	args := []interface{}{date}

	for i, userID := range registerUsers {
		placeholders[i] = "?"
		args = append(args, userID)
	}

	// 正确构建 IN 子句
	inClause := strings.Join(placeholders, ", ")
	query := fmt.Sprintf(`SELECT DISTINCT user_id FROM user_login WHERE date = ? AND user_id IN (%s)`, inClause)

	if channel != "" {
		query += ` AND channel = ?`
		args = append(args, channel)
	}

	rows, err := chdb.Default().Table(mbi.TableUserLogin).Raw(query, args...).Rows()
	if err != nil {
		return nil, fmt.Errorf("failed to query login users: %w", err)
	}
	defer rows.Close()

	for rows.Next() {
		var userID int64
		if err := rows.Scan(&userID); err != nil {
			log.Errorf("Failed to scan user ID: %v", err)
			continue
		}
		users = append(users, userID)
	}

	return users, nil
}

// addDays 给日期添加天数 (格式: 20250129 -> 20250130)
func addDays(date int32, days int) int32 {
	dateStr := fmt.Sprintf("%d", date)
	if len(dateStr) != 8 {
		return date // 格式错误，返回原值
	}

	year, _ := strconv.Atoi(dateStr[:4])
	month, _ := strconv.Atoi(dateStr[4:6])
	day, _ := strconv.Atoi(dateStr[6:8])

	t := time.Date(year, time.Month(month), day, 0, 0, 0, 0, time.UTC)
	newDate := t.AddDate(0, 0, days)

	return int32(newDate.Year()*10000 + int(newDate.Month())*100 + newDate.Day())
}

// 导航模块管理相关处理函数
// onGMGetNavigationModuleListReq 获取导航模块列表
func (uc *useCase) onGMGetNavigationModuleListReq(ctx *gin.Context, body *pb.GMGetNavigationModuleListReq) {
	var modules []*common.NavigationModule
	query := mdb.Default().Table(common.TableNameNavigationModule).Where("1=1")

	// 可选的父级ID过滤
	if body.ParentID > 0 {
		query = query.Where("parent_id = ?", body.ParentID)
	}
	// 可选的版块过滤
	if body.Section != "" {
		query = query.Where("section = ?", body.Section)
	}

	// 按排序ID和ID排序
	err := query.Order("sort_id ASC, id ASC").Find(&modules).Error
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}

	// 转换为protobuf格式
	list := make([]*pb.NavigationModule, 0, len(modules))
	for _, module := range modules {
		list = append(list, module.ToPB())
	}

	hs.OK(ctx, pb.GMGetNavigationModuleListResp{
		Code: pb.SUCCESS,
		List: list,
	})
}

// onGMAddNavigationModuleReq 添加导航模块
func (uc *useCase) onGMAddNavigationModuleReq(ctx *gin.Context, body *pb.GMAddNavigationModuleReq) {
	// 验证必填字段
	if body.ModuleName == "" {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "模块名称不能为空"})
		return
	}
	if body.ModuleID == "" {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "模块ID不能为空"})
		return
	}

	// 检查模块ID是否已存在
	var count int64
	err := mdb.Default().Table(common.TableNameNavigationModule).
		Where("module_id = ? AND parent_id = ?", body.ModuleID, body.ParentID).
		Count(&count).Error
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	if count > 0 {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "模块ID已存在"})
		return
	}

	// 创建新模块
	module := &common.NavigationModule{
		SortID:      body.SortID,
		Section:     body.Section,
		ModuleName:  body.ModuleName,
		LangID:      body.LangID,
		ModuleID:    body.ModuleID,
		ModuleIcon:  body.ModuleIcon,
		JumpAddress: body.JumpAddress,
		ParentID:    body.ParentID,
		IsActive:    body.IsActive,
	}

	err = mdb.Default().Table(common.TableNameNavigationModule).Create(module).Error
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}

	hs.OK(ctx, pb.GMAddNavigationModuleResp{
		Code: pb.SUCCESS,
		Data: module.ToPB(),
	})
}

// onGMUpdateNavigationModuleReq 更新导航模块
func (uc *useCase) onGMUpdateNavigationModuleReq(ctx *gin.Context, body *pb.GMUpdateNavigationModuleReq) {
	// 验证必填字段
	if body.ID <= 0 {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "模块ID无效"})
		return
	}
	if body.ModuleName == "" {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "模块名称不能为空"})
		return
	}
	if body.ModuleID == "" {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "模块ID不能为空"})
		return
	}

	// 检查模块是否存在
	var existingModule common.NavigationModule
	err := mdb.Default().Table(common.TableNameNavigationModule).
		Where("id = ?", body.ID).First(&existingModule).Error
	if err != nil {
		if err.Error() == "record not found" {
			hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "模块不存在"})
		} else {
			log.Error(err)
			hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		}
		return
	}

	// 检查模块ID是否与其他模块冲突
	var count int64
	err = mdb.Default().Table(common.TableNameNavigationModule).
		Where("module_id = ? AND parent_id = ? AND id != ?", body.ModuleID, body.ParentID, body.ID).
		Count(&count).Error
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	if count > 0 {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "模块ID已存在"})
		return
	}

	// 更新模块信息
	updateData := map[string]interface{}{
		"sort_id":      body.SortID,
		"section":      body.Section,
		"module_name":  body.ModuleName,
		"lang_id":      body.LangID,
		"module_id":    body.ModuleID,
		"module_icon":  body.ModuleIcon,
		"jump_address": body.JumpAddress,
		"parent_id":    body.ParentID,
		"is_active":    body.IsActive,
		"updated_at":   time.Now().Unix(),
	}

	err = mdb.Default().Table(common.TableNameNavigationModule).
		Where("id = ?", body.ID).Updates(updateData).Error
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}

	// 获取更新后的模块信息
	var updatedModule common.NavigationModule
	err = mdb.Default().Table(common.TableNameNavigationModule).
		Where("id = ?", body.ID).First(&updatedModule).Error
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}

	hs.OK(ctx, pb.GMUpdateNavigationModuleResp{
		Code: pb.SUCCESS,
		Data: updatedModule.ToPB(),
	})
}

// onGMDeleteNavigationModuleReq 删除导航模块
func (uc *useCase) onGMDeleteNavigationModuleReq(ctx *gin.Context, body *pb.GMDeleteNavigationModuleReq) {
	if body.ID <= 0 {
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "模块ID无效"})
		return
	}

	// 检查模块是否存在
	var existingModule common.NavigationModule
	err := mdb.Default().Table(common.TableNameNavigationModule).
		Where("id = ?", body.ID).First(&existingModule).Error
	if err != nil {
		if err.Error() == "record not found" {
			hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "模块不存在"})
		} else {
			log.Error(err)
			hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		}
		return
	}

	var deletedCount int32 = 0

	// 开始事务
	tx := mdb.Default().Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 如果需要删除子模块
	if body.DeleteChildren {
		// 递归删除所有子模块
		deletedCount, err = uc.deleteModuleAndChildren(tx, body.ID)
		if err != nil {
			tx.Rollback()
			log.Error(err)
			hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
			return
		}
	} else {
		// 检查是否有子模块
		var childCount int64
		err = tx.Table(common.TableNameNavigationModule).
			Where("parent_id = ?", body.ID).Count(&childCount).Error
		if err != nil {
			tx.Rollback()
			log.Error(err)
			hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
			return
		}

		if childCount > 0 {
			tx.Rollback()
			hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "存在子模块，请先删除子模块或选择同时删除子模块"})
			return
		}

		// 只删除当前模块
		err = tx.Table(common.TableNameNavigationModule).Where("id = ?", body.ID).Delete(&common.NavigationModule{}).Error
		if err != nil {
			tx.Rollback()
			log.Error(err)
			hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
			return
		}
		deletedCount = 1
	}

	// 提交事务
	if err = tx.Commit().Error; err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}

	hs.OK(ctx, pb.GMDeleteNavigationModuleResp{
		Code:         pb.SUCCESS,
		DeletedCount: deletedCount,
	})
}

// deleteModuleAndChildren 递归删除模块及其所有子模块
func (uc *useCase) deleteModuleAndChildren(tx *gorm.DB, moduleID int32) (int32, error) {
	var deletedCount int32 = 0

	// 获取所有子模块
	var children []common.NavigationModule
	err := tx.Table(common.TableNameNavigationModule).Where("parent_id = ?", moduleID).Find(&children).Error
	if err != nil {
		return 0, err
	}

	// 递归删除所有子模块
	for _, child := range children {
		childDeletedCount, err := uc.deleteModuleAndChildren(tx, child.ID)
		if err != nil {
			return 0, err
		}
		deletedCount += childDeletedCount
	}

	// 删除当前模块
	err = tx.Table(common.TableNameNavigationModule).Where("id = ?", moduleID).Delete(&common.NavigationModule{}).Error
	if err != nil {
		return 0, err
	}
	deletedCount++

	return deletedCount, nil
}
