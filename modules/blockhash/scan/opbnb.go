package scan

import (
	"encoding/json"
	"fmt"
	"s2/pb"
	"strconv"

	"github.com/jfcwrlight/core/conf"
	"github.com/jfcwrlight/core/utils/hs"
)

var OPBNB = newOPBNBScan()

type opBNB struct {
	APIKey string
}

func newOPBNBScan() *opBNB {
	key := conf.Str("opbnb.APIKey")
	scan := &opBNB{APIKey: key}
	return scan
}

func (scan *opBNB) Type() pb.EnumChain {
	return pb.CHAIN_OPBNB
}

func (scan *opBNB) Latest() (int64, string, error) {
	url := fmt.Sprintf("https://api-opbnb.bscscan.com/api?module=proxy&action=eth_getBlockByNumber&tag=latest&boolean=false&apikey=%s", scan.APIKey)
	data, err := hs.HttpRequest("GET", url, nil, nil)
	if err != nil {
		return 0, "", err
	}
	resp := struct {
		Result struct {
			Number string `json:"number"`
			Hash   string `json:"hash"`
		} `json:"result"`
	}{}
	err = json.Unmarshal(data, &resp)
	if err != nil {
		return 0, "", fmt.Errorf("unmarshal %s error: %s", string(data), err.Error())
	}
	blockNumber, err := strconv.ParseInt(resp.Result.Number[2:], 16, 64)
	if err != nil {
		return 0, "", err
	}
	return blockNumber, resp.Result.Hash[2:], nil
}
