package order

import (
	"fmt"
	"time"

	"s2/common/cache"
	"s2/modules/order/chains"
	"s2/modules/order/coins"
	"s2/pb"

	"github.com/jfcwrlight/core/infra/mdb"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/message"
	"github.com/jfcwrlight/core/system"
	"github.com/shopspring/decimal"
	"gorm.io/gorm/clause"
)

// CreateWithdrawOrder 创建提款订单
func CreateWithdrawOrder(userID int64, chain string, coinType string, amount string, decimals int64, assetId int32, assetAmount float64, assetFees float64, toAddress string) (*WithdrawOrder, error) {
	bc, err := cache.QueryUserBasicInfo(userID)
	if err != nil {
		return nil, fmt.Errorf("CreateWithdrawOrder failed to query user basic info:uid: %d, %w", userID, err)
	}
	order := &WithdrawOrder{
		ID:          CreateOrderId(userID),
		UserID:      userID,
		Chain:       chain,
		CoinType:    coinType,
		Amount:      amount,
		Decimals:    decimals,
		AssetId:     assetId,
		AssetAmount: assetAmount,
		AssetFees:   assetFees,
		ToAddress:   toAddress,
		Status:      int32(pb.OrderStatus_CHECKING),
		ParentID:    bc.ParentID,
		Channel:     bc.Channel,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	if err := mdb.Default().Create(order).Error; err != nil {
		return nil, fmt.Errorf("failed to create withdraw order: %v", err)
	}
	return order, nil
}

// UpdateWithdrawOrderStatus 更新提款订单状态
func UpdateWithdrawOrderStatus(id string, status pb.EnumOrderStatus, channel string) (*WithdrawOrder, error) {
	if status == pb.OrderStatus_CHECKING {
		return nil, fmt.Errorf("不能更新提款订单为待审核 id: %d", id)
	}
	// 查询订单
	orders := []*WithdrawOrder{}
	tx := mdb.Default().TxReadCommit()
	defer mdb.RecoverWithRollback(tx)
	err := tx.Clauses(clause.Locking{Strength: "UPDATE"}).
		Where("id = ?", id).Find(&orders).Error
	if err != nil {
		tx.Rollback()
		return nil, err
	}
	if len(orders) == 0 {
		tx.Rollback()
		return nil, fmt.Errorf("order %s not found", id)
	}
	order := orders[0]
	if channel != "" {
		if order.Channel != channel {
			tx.Rollback()
			return nil, fmt.Errorf("order %s is not in channel %s", order.ID, channel)
		}
	}
	if order.Status == int32(pb.OrderStatus_CONFIRMED) || order.Status == int32(pb.OrderStatus_FAILED) {
		tx.Rollback()
		return nil, fmt.Errorf("order %s is already processed with status %d", order.ID, order.Status)
	}
	//人工审核订单状态必须在Checking
	if status == pb.OrderStatus_PENDING || status == pb.OrderStatus_REJECTED_PENDING {
		if order.Status != int32(pb.OrderStatus_CHECKING) {
			tx.Rollback()
			return nil, fmt.Errorf("order %s is already processed with status %d ,must be checking", order.ID, order.Status)
		}
		//要设置订单状态完成或者失败订单状态必须在Pending
	} else if status == pb.OrderStatus_CONFIRMED || status == pb.OrderStatus_FAILED {
		if order.Status != int32(pb.OrderStatus_PENDING) {
			tx.Rollback()
			return nil, fmt.Errorf("order %s is already processed with status %d ,must be pending", order.ID, order.Status)
		}
	} else if status == pb.OrderStatus_REJECTED_CONFIRMED { //驳回结算订单状态必须在RejectedPending
		if order.Status != int32(pb.OrderStatus_REJECTED_PENDING) {
			tx.Rollback()
			return nil, fmt.Errorf("order %s is already processed with status %d ,must be rejected pending", order.ID, order.Status)
		}
	}
	order.Status = int32(status)
	order.UpdatedAt = time.Now()
	if status == pb.OrderStatus_FAILED || status == pb.OrderStatus_REJECTED_CONFIRMED {
		err = tx.Table(confirmedWithdrawTableName(time.Unix(int64(order.CreatedAt.Unix()), 0))).Create(&order).Error
		if err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("failed to update order status: %w", err)
		}
		err = tx.Delete(order).Error
		if err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("failed to delete order: %w", err)
		}
	} else {
		err = tx.Updates(order).Error
		if err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("failed to update order status: %w", err)
		}
	}
	tx.Commit()
	if order.Status == int32(pb.OrderStatus_REJECTED_PENDING) {
		// 审核驳回待退款
		uc, err := cache.QueryUserBasicInfo(order.UserID)
		if err == nil {
			message.Stream.Cast(uc.ServerID, &pb.UserWithdrawRejectedMsg{
				UserID: order.UserID,
				ID:     order.ID,
			})
		}
	}
	log.Infof("Order %s has been confirmed and moved to the confirmed orders table", id)
	return order, nil
}

// CheckAndSubmitOrder 业务服务器 RPC 请求核对并提交订单
func CheckAndSubmitWithdrawOrder(id string) (*WithdrawOrder, error) {
	return UpdateWithdrawOrderStatus(id, pb.OrderStatus_REJECTED_CONFIRMED, "")
}

// 提款
func withdrawLoop() {
	processPendingWithdraws()
	processRejectedPendingWithdraws()
	ticker := time.NewTicker(time.Second * 60)
	for {
		select {
		case <-system.RootCtx().Done():
			return
		case <-ticker.C:
			processPendingWithdraws()
			processRejectedPendingWithdraws()
		}
	}
}
func processRejectedPendingWithdraws() error {
	var orders []*WithdrawOrder
	query := mdb.Default().Model(&WithdrawOrder{})
	query = query.Where("status = ?", pb.OrderStatus_REJECTED_PENDING)
	query = query.Where("created_at >= ?", time.Now().Add(-time.Hour*24*7))
	if err := query.Find(&orders).Error; err != nil {
		return fmt.Errorf("failed to get pending withdraw orders: %v", err)
	}
	for _, order := range orders {
		uc, err := cache.QueryUserBasicInfo(order.UserID)
		if err == nil {
			message.Stream.Cast(uc.ServerID, &pb.UserWithdrawRejectedMsg{
				UserID: order.UserID,
				ID:     order.ID,
			})
		}
	}
	return nil
}

// processPendingWithdraws 处理审核通过的提款订单
func processPendingWithdraws() error {
	var orders []*WithdrawOrder
	query := mdb.Default().Model(&WithdrawOrder{})
	query = query.Where("status = ?", pb.OrderStatus_PENDING)
	query = query.Where("created_at >= ?", time.Now().Add(-time.Hour*24*7))
	if err := query.Find(&orders).Error; err != nil {
		return fmt.Errorf("failed to get pending withdraw orders: %v", err)
	}
	// 处理审核通过的提款订单
	for _, order := range orders {
		// 获取对应的链
		chain, ok := chains.Get(pb.EnumChain(pb.EnumChain_value[order.Chain]))
		if !ok {
			log.Errorf("chain not found: %s", order.Chain)
			if _, err := UpdateWithdrawOrderStatus(order.ID, pb.OrderStatus_FAILED, ""); err != nil {
				log.Errorf("failed to update order status: %v", err)
			}
			continue
		}
		// 获取对应的代币
		coin, ok := coins.Get(pb.EnumCoinSymbol(pb.EnumCoinSymbol_value[order.CoinType]))
		if !ok {
			log.Errorf("coin not found: %s", order.CoinType)
			if _, err := UpdateWithdrawOrderStatus(order.ID, pb.OrderStatus_FAILED, ""); err != nil {
				log.Errorf("failed to update order status: %v", err)
			}
			continue
		}
		// 计算提款金额
		amount, err := decimal.NewFromString(order.Amount)
		if err != nil {
			log.Errorf("invalid amount format: %s", order.Amount)
			if _, err := UpdateWithdrawOrderStatus(order.ID, pb.OrderStatus_FAILED, ""); err != nil {
				log.Errorf("failed to update order status: %v", err)
			}
			continue
		}
		amount = amount.Div(decimal.NewFromInt(order.Decimals))
		// 执行提款
		err = chain.WithdrawOrder(amount.InexactFloat64(), coin, order.ToAddress)
		if err != nil {
			log.Errorf("failed to process withdraw order %d: %v", order.ID, err)
			if _, err := UpdateWithdrawOrderStatus(order.ID, pb.OrderStatus_FAILED, ""); err != nil {
				log.Errorf("failed to update order status: %v", err)
			}
			continue
		}
		// 更新订单状态为已确认
		if _, err := UpdateWithdrawOrderStatus(order.ID, pb.OrderStatus_CONFIRMED, ""); err != nil {
			log.Errorf("failed to update order status: %v", err)
			continue
		}
		log.Infof("Successfully processed withdraw order %d", order.ID)
	}
	return nil
}
