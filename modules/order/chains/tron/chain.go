package tron

import (
	"context"
	"fmt"
	"math"
	"math/big"
	"strconv"
	"sync"
	"time"

	"s2/common/web3"
	"s2/modules/order/chains"
	"s2/modules/order/coins"
	"s2/modules/order/stypes"
	"s2/modules/order/wallet"
	"s2/pb"

	"encoding/hex"

	"crypto/sha256"

	"github.com/ethereum/go-ethereum/crypto"
	"github.com/fbsobreira/gotron-sdk/pkg/address"
	"github.com/fbsobreira/gotron-sdk/pkg/client"
	apipb "github.com/fbsobreira/gotron-sdk/pkg/proto/api"
	coreproto "github.com/fbsobreira/gotron-sdk/pkg/proto/core"
	"github.com/jfcwrlight/core/basic/events"
	"github.com/jfcwrlight/core/conf"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/utils"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/protobuf/proto"
)

var (
	walletKey         string
	collectionAddress string
	gasPrivate        string
	withdrawPrivate   string
	scanDelay         uint64
	intervalBlock     uint64
	DelegateEnergyMin uint64 = 65000 * 2      // 最低能量值
	UserMinUse        uint64 = 1000000        // 用户最小可用 USDT 数量（单位：SUN）
	TransferTRXLimit  int64  = 100000         // TRX 转账最小值（单位：SUN）
	LeftUsd           uint64 = 1000           // 保留的 USDT 数量（单位：SUN）
	DelegateTRXLimit  uint64 = 7777000000 * 2 // 代理资源的 TRX 数量（单位：SUN）
	TRONDecimals      uint64 = 1000000
)

// Chain TRON 链实现
// TODO: 补充区块扫描、归集、提款逻辑

// Chain 定义 TRON 链实例
// client: TRON gRPC 客户端
// native: 原生 TRX
// contracts: TRC20 代币列表
// lastBlock: 上次扫描的区块号
// mu: 并发保护锁
type Chain struct {
	client    *client.GrpcClient
	native    *NativeToken
	contracts map[string]*TRC20Token
	lastBlock uint64
	mu        sync.Mutex
}

// New 创建 TRON 链实例
func New() (*Chain, error) {
	// 初始化配置
	walletKey = conf.Str("chain.walletKey", "")
	collectionAddress = conf.Str("chain.tron.collectionAddress", "")
	gasPrivate = conf.Str("chain.tron.gasPrivate", "")
	withdrawPrivate = conf.Str("chain.tron.withdrawPrivate", "")
	scanDelay = uint64(conf.Num[int]("chain.tron.delay", 10))
	intervalBlock = uint64(conf.Num[int]("chain.tron.intervalblock", 0))
	// 获取 RPC 地址并连接 TRON 节点
	rpc := conf.Str("chain.tron.rpcURL", "")
	cli := client.NewGrpcClient(rpc)
	// Start the gRPC client connection with insecure credentials
	if err := cli.Start(grpc.WithTransportCredentials(insecure.NewCredentials())); err != nil {
		log.Errorf("failed to start TRON gRPC client: %v", err)
		return nil, err
	}
	chain := &Chain{
		client:    cli,
		contracts: make(map[string]*TRC20Token),
	}

	// 原生币 TRX 实例
	chain.native = NewNativeToken(cli, chain)
	coins.Reg(chain.native)

	// 注册 TRC20 代币
	usdtAddr := conf.Str("chain.tron.USDT.address", "")
	if usdtAddr != "" {
		limit := conf.Num[float64]("chain.tron.USDT.collectionLimit", 0.99)
		rechargeOpen := conf.Bool("chain.tron.USDT.rechargeOpen", false)
		withdrawOpen := conf.Bool("chain.tron.USDT.withdrawOpen", false)
		token := NewTRC20Token(cli, chain, usdtAddr, pb.TRONUSDT, 1e6, pb.USDT, limit, rechargeOpen, withdrawOpen, pb.CoinConfig{
			AssetId:      int64(conf.Num[int]("chain.tron.USDT.AssetId", 0)),
			ExchangeRate: conf.Num[float64]("chain.tron.USDT.AssetExchangeRate", 0),
			Fees:         conf.Num[float64]("chain.tron.USDT.Fees", 0),
			MinWithdraw:  conf.Num[float64]("chain.tron.USDT.MinWithdraw", 0),
			MinRecharge:  conf.Num[float64]("chain.tron.USDT.MinRecharge", 0),
		})
		chain.contracts[token.contractAddress] = token
		coins.Reg(token)
	}

	// 获取并设置上次扫描区块号
	lastStr, err := chains.GetLastScanInfo(chain.Type().String())
	if err != nil {
		return nil, fmt.Errorf("failed to get latest scanned block: %w", err)
	}
	if lastStr != "" {
		n, err := strconv.ParseUint(lastStr, 10, 64)
		if err != nil {
			log.Error(err)
			return nil, err
		}
		chain.lastBlock = n
	} else {
		blk, err := cli.GetNowBlock()
		if err != nil {
			log.Errorf("failed to get latest block: %v", err)
			return nil, err
		}
		chain.lastBlock = uint64(blk.GetBlockHeader().GetRawData().GetNumber())
		lastStr = strconv.Itoa(int(chain.lastBlock))
		// 保存最新区块号
		if err := chains.SaveLatestScanInfo(chain.Type().String(), lastStr); err != nil {
			return nil, fmt.Errorf("failed to save latest block: %w", err)
		}
	}

	// 注册链
	chains.Reg(chain)
	return chain, nil
}

// Type 返回链类型
func (c *Chain) Type() pb.EnumChain {
	return pb.CHAIN_TRON
}

// GetUserRechargeAddress 获取用户充值地址
func (c *Chain) GetUserRechargeAddress(userID int64) (string, string, error) {
	wa, err := wallet.GetRechargeAddress(userID, c)
	if err != nil {
		log.Error(err)
		return "", "", err
	}
	if wa != nil {
		return wa.Address, "", nil
	}
	priv, _ := c.GetUserRechargePrivate(userID)
	address, err := web3.GetTRONAddressFromPrivateKey(priv)
	if err != nil {
		log.Error(err)
		return "", "", fmt.Errorf("failed to derive tron address: %v", err)
	}
	err = wallet.SaveRechargeAddress(userID, c, address, "")
	if err != nil {
		log.Error(err)
		return "", "", err
	}
	return address, "", nil
}

// GetUserRechargePrivate 生成用户充值私钥
func (c *Chain) GetUserRechargePrivate(userID int64) (string, error) {
	in := walletKey + strconv.Itoa(int(userID))
	key := in[:16]
	x := in[len(in)-16:]
	privatekey := web3.StringToKey256([]byte(key), []byte(x), []byte(in), 1)
	return privatekey, nil
}

// StartScan 启动区块扫描
func (c *Chain) StartScan(ctx context.Context) error {
	log.Infof("Starting TRON scan at block %d", c.lastBlock)
	ticker := time.NewTicker(time.Second * 3)
	defer ticker.Stop()
	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-ticker.C:
			c.doScan(ctx)
		}
	}
}

// 开始扫描
func (c *Chain) doScan(ctx context.Context) error {
	defer utils.RecoverPanic()
	// 调试模式指定区块
	debugBlockNumber := conf.Num[int]("chain.tron.debugblocknumber", 0)
	if debugBlockNumber > 0 {
		c.scanBlock(uint64(debugBlockNumber))
		return nil
	}
	for {
		if intervalBlock == 0 {
			return nil
		}
		// 获取最新区块
		blk, err := c.client.GetNowBlock()
		if err != nil {
			time.Sleep(time.Second)
			log.Errorf("failed to get latest block: %v", err)
			continue
		}
		block := uint64(blk.GetBlockHeader().GetRawData().GetNumber())
		from := c.lastBlock + 1
		end := block - scanDelay
		if end < from {
			time.Sleep(time.Second * 3)
			continue
		}
		for i := from; i <= end; i++ {
			if utils.ContextDone(ctx) {
				return ctx.Err()
			}
			if block-c.lastBlock > 30 {
				log.Warnf("TRON扫描区块延迟太大: %d", block-c.lastBlock)
			}
			err := c.scanBlock(i)
			if err != nil {
				log.Errorf("failed to scan block %d: %v", i, err)
				continue
			}
			c.lastBlock = i
			if err := chains.SaveLatestScanInfo(c.Type().String(), strconv.Itoa(int(c.lastBlock))); err != nil {
				log.Errorf("failed to save latest block: %w", err)
				time.Sleep(time.Second)
				continue
			}
		}
	}
}

// scanBlock 扫描单个区块，解析 TRX 和 TRC20 转账 要过滤pending，失败的交易
func (c *Chain) scanBlock(blockNumber uint64) error {
	// 解析原生 TRX 转账
	if c.native.RechargeOpen() {
		blockExt, err := c.client.GetBlockByNum(int64(blockNumber))
		if err != nil {
			return fmt.Errorf("failed to get block %d: %w", blockNumber, err)
		}
		for _, txExt := range blockExt.GetTransactions() {
			if res := txExt.GetResult(); res == nil || !res.GetResult() {
				continue
			}
			txHash := hex.EncodeToString(txExt.GetTxid())
			tx := txExt.GetTransaction()
			if tx == nil {
				continue
			}
			for _, ctn := range tx.GetRawData().GetContract() {
				if ctn.GetType() == coreproto.Transaction_Contract_TransferContract {
					tc := &coreproto.TransferContract{}
					if err := ctn.GetParameter().UnmarshalTo(tc); err != nil {
						log.Error(err)
						continue
					}
					from := address.Address(tc.GetOwnerAddress()).String()
					to := address.Address(tc.GetToAddress()).String()
					exist, _, err := wallet.ContainsAddress(c.Type(), to)
					if err != nil {
						log.Error(err)
						continue
					}
					if !exist {
						continue
					}
					amount := big.NewInt(tc.GetAmount())
					events.Publish(stypes.EventTransfer{
						TxHash:    txHash,
						Coin:      c.native.CoinSymbol(),
						Chain:     c.Type(),
						From:      from,
						To:        to,
						Value:     amount,
						Timestamp: uint64(time.Now().Unix()),
					})
				}
			}
		}
	}
	// 解析 TRC20 代币转账事件 获取交易信息（含日志）
	txInfos, err := c.client.GetBlockInfoByNum(int64(blockNumber))
	if err != nil {
		return fmt.Errorf("failed to get block info %d: %w", blockNumber, err)
	}
	// 遍历交易及日志
	for _, txInfo := range txInfos.GetTransactionInfo() {
		if txInfo.GetResult() != coreproto.TransactionInfo_SUCESS {
			continue
		}
		txHash := hex.EncodeToString(txInfo.GetId())
		for _, lg := range txInfo.GetLog() {
			// lg.GetAddress() yields 20-byte raw contract address; add Tron prefix 0x41 for base58
			rawAddr := lg.GetAddress()
			fullAddr := make([]byte, address.AddressLength)
			fullAddr[0] = address.TronBytePrefix
			copy(fullAddr[1:], rawAddr)
			contractAddr := address.Address(fullAddr).String()
			token, ok := c.contracts[contractAddr]
			if !ok {
				continue
			}
			ev := token.ParseLog(lg)
			if ev == nil {
				continue
			}
			toAddr := ev.To
			from := ev.From
			exist, _, err := wallet.ContainsAddress(c.Type(), toAddr)
			if err != nil {
				log.Error(err)
				continue
			}
			if !exist {
				continue
			}
			events.Publish(stypes.EventTransfer{
				TxHash:    txHash,
				Coin:      token.CoinSymbol(),
				Chain:     c.Type(),
				From:      from,
				To:        toAddr,
				Value:     ev.Value,
				Timestamp: uint64(time.Now().Unix()),
			})
		}
	}
	return nil
}
func (c *Chain) collectionNative(coin stypes.ICoin, private string) (*big.Int, error) {
	addr, err := web3.GetTRONAddressFromPrivateKey(private)
	if err != nil {
		return nil, fmt.Errorf("failed to derive address: %v", err)
	}
	resource, err := c.GetAccountResource(collectionAddress)
	if err != nil {
		return nil, fmt.Errorf("failed to get account resource: %v", err)
	}
	// leftEnergy := resource.EnergyLimit - resource.EnergyUsed
	leftBandwidth := resource.NetLimit - resource.NetUsed
	if leftBandwidth < 300 {
		return nil, fmt.Errorf("bandwidth is not enough")
	}
	fee := big.NewInt(1000000)
	bal, err := coin.Balance(addr)
	if err != nil {
		return nil, fmt.Errorf("failed to get native balance: %v", err)
	}
	if bal.Cmp(fee) <= 0 {
		return nil, fmt.Errorf("native balance is less than 1 TRX")
	}
	bal.Sub(bal, fee)
	// 归集到主归集地址
	_, err = coin.Transfer(private, collectionAddress, bal)
	if err != nil {
		return nil, fmt.Errorf("failed to collect native TRX: %v", err)
	}
	return bal, nil
}
func (c *Chain) collectionToken(coin stypes.ICoin, private string) (*big.Int, error) {
	// 代币：TRC20 归集：先检查 allowance，若无则代理资源、Approve，最后用 TransferFrom
	// 从私钥派生地址
	gasAddress, err := web3.GetTRONAddressFromPrivateKey(gasPrivate)
	if err != nil {
		return nil, fmt.Errorf("failed to derive gas address: %v", err)
	}
	userAddress, err := web3.GetTRONAddressFromPrivateKey(private)
	if err != nil {
		return nil, fmt.Errorf("failed to derive address: %v", err)
	}
	// 类型断言为 TRC20Token，使用 TransferFrom
	trc20, ok := coin.(*TRC20Token)
	if !ok {
		return nil, fmt.Errorf("coin is not a TRC20 token")
	}
	// 检查 allowance
	allow, err := trc20.Allowance(userAddress, gasAddress)
	if err != nil {
		return nil, fmt.Errorf("failed to get allowance: %v", err)
	}
	if allow.Sign() <= 0 {
		trxBal, err := c.native.Balance(userAddress)
		if err != nil {
			return nil, fmt.Errorf("failed to get native balance: %v", err)
		}
		fee := big.NewInt(TransferTRXLimit)
		if trxBal.Cmp(fee) < 0 {
			_, err := c.native.Transfer(gasPrivate, userAddress, fee)
			if err != nil {
				return nil, fmt.Errorf("failed to top-up TRX for fee: %v", err)
			}
		}
		userResourceBefore, err := c.GetAccountResource(userAddress)
		if err != nil {
			return nil, fmt.Errorf("failed to get account resource: %v", err)
		}
		leftNet := userResourceBefore.FreeNetLimit + userResourceBefore.NetLimit - userResourceBefore.FreeNetUsed - userResourceBefore.NetUsed
		if leftNet < 400 {
			return nil, fmt.Errorf("bandwidth is not enough")
		}
		leftEnergy := userResourceBefore.EnergyLimit - userResourceBefore.EnergyUsed
		if leftEnergy < int64(DelegateEnergyMin-1000) {
			gasResources, err := c.GetAccountResource(gasAddress)
			leftEnergy := gasResources.EnergyLimit - gasResources.EnergyUsed
			if leftEnergy < int64(DelegateEnergyMin) {
				return nil, fmt.Errorf("gas energy is not enough")
			}
			_, err = c.DelegateResourceEnergy(gasPrivate, userAddress, int64(DelegateTRXLimit))
			if err != nil {
				return nil, fmt.Errorf("failed to delegate energy: %v", err)
			}
			time.Sleep(5 * time.Second)
		}
		userResource, err := c.GetAccountResource(userAddress)
		if err != nil {
			return nil, fmt.Errorf("failed to get account resource: %v", err)
		}
		leftEnergy = userResource.EnergyLimit - userResource.EnergyUsed
		if leftEnergy < int64(DelegateEnergyMin-1000) {
			return nil, fmt.Errorf("energy is not enough t2")
		}
		txid, err := trc20.Approve(private, gasAddress, big.NewInt(math.MaxInt64))
		if err != nil {
			return nil, fmt.Errorf("failed to approve token: %v", err)
		}
		log.Infof("Approve success: tx=%s userAddress=%s gas=%s", txid, userAddress, gasAddress)

		txid, err = c.UnDelegateResourceEnergy(gasPrivate, userAddress, int64(DelegateTRXLimit))
		if err != nil {
			log.Errorf("failed to undelegate energy: %v", err)
		}
	}
	bal, err := trc20.Balance(userAddress)
	if err != nil {
		return nil, fmt.Errorf("failed to get balance: %v", err)
	}
	bal.Sub(bal, big.NewInt(int64(LeftUsd)))
	gasResource, err := c.GetAccountResource(gasAddress)
	if err != nil {
		return nil, fmt.Errorf("failed to get account resource: %v", err)
	}
	leftNet := gasResource.FreeNetLimit + gasResource.NetLimit - gasResource.FreeNetUsed - gasResource.NetUsed
	if leftNet < 400 {
		return nil, fmt.Errorf("gasResource bandwidth is not enough")
	}
	leftEnergy := gasResource.EnergyLimit - gasResource.EnergyUsed
	if leftEnergy < int64(DelegateEnergyMin) {
		return nil, fmt.Errorf("gasResource energy is not enough")
	}
	// 使用 TransferFrom 归集代币
	_, err = trc20.TransferFrom(gasPrivate, userAddress, collectionAddress, bal)
	if err != nil {
		return nil, fmt.Errorf("failed to collect token: %v", err)
	}
	return bal, nil
}

// Collection 归集
func (c *Chain) Collection(coin stypes.ICoin, private string) (*big.Int, error) {
	c.mu.Lock()
	defer c.mu.Unlock()
	// 区分原生 TRX 和 TRC20
	if coin.CoinSymbol() == c.native.CoinSymbol() {
		return c.collectionNative(coin, private)
	}
	return c.collectionToken(coin, private)
}

// WithdrawOrder 实现提款功能
func (c *Chain) WithdrawOrder(amount float64, coin stypes.ICoin, address string) error {
	c.mu.Lock()
	defer c.mu.Unlock()
	if !web3.IsValidTRONAddress(address) {
		return fmt.Errorf("invalid address: %s", address)
	}
	if address == "" {
		return fmt.Errorf("invalid address: %s", address)
	}
	withdrawAddress, err := web3.GetTRONAddressFromPrivateKey(withdrawPrivate)
	if err != nil {
		return fmt.Errorf("failed to derive withdraw address: %v", err)
	}
	resource, err := c.GetAccountResource(withdrawAddress)
	if err != nil {
		return fmt.Errorf("failed to get account resource: %v", err)
	}
	leftNet := resource.FreeNetLimit + resource.NetLimit - resource.FreeNetUsed - resource.NetUsed
	if leftNet < 300 {
		return fmt.Errorf("bandwidth is not enough")
	}
	leftEnergy := resource.EnergyLimit - resource.EnergyUsed
	if leftEnergy < int64(DelegateEnergyMin*2) {
		return fmt.Errorf("energy is not enough: %d < %d", leftEnergy, DelegateEnergyMin*2)
	}
	bal, err := coin.Balance(withdrawAddress)
	if err != nil {
		return fmt.Errorf("failed to get balance: %v", err)
	}
	amt := new(big.Int)
	amt.SetInt64(int64(amount * float64(coin.CoinDecimals())))
	if bal.Cmp(amt) < 0 {
		return fmt.Errorf("balance is not enough: %d < %d", bal.Int64(), amt.Int64())
	}
	// 扣费方为 withdrawPrivate
	_, err = coin.Transfer(withdrawPrivate, withdrawAddress, amt)
	if err != nil {
		return fmt.Errorf("TRON failed to withdraw: %v", err)
	}
	log.Infof("TRON Withdraw success: amount=%f, coin=%s, address=%s", amount, coin.CoinSymbol().String(), address)
	return nil
}

// IsOfficialAddress 官方地址判断
func (c *Chain) IsOfficialAddress(address string) bool {
	return address == collectionAddress
}

// GetAccountResource 查看指定地址的能量和带宽
func (c *Chain) GetAccountResource(address string) (*apipb.AccountResourceMessage, error) {
	return c.client.GetAccountResource(address)
}

// GetCanDelegatedMax 查看指定地址最大可代理资源数
func (c *Chain) GetCanDelegatedMax(address string, resource coreproto.ResourceCode) (int64, error) {
	msg, err := c.client.GetCanDelegatedMaxSize(address, int32(resource))
	if err != nil {
		return 0, fmt.Errorf("failed to get max delegatable size: %v", err)
	}
	return msg.GetMaxSize(), nil
}

// DelegateResourceBandwidth 代理带宽
func (c *Chain) DelegateResourceBandwidth(fromPrivate, to string, amount int64) (string, error) {
	fromAddr, err := web3.GetTRONAddressFromPrivateKey(fromPrivate)
	if err != nil {
		return "", fmt.Errorf("invalid private key: %v", err)
	}
	// build transaction and optionally sign and broadcast
	txExt, err := c.client.DelegateResource(fromAddr, to, coreproto.ResourceCode_BANDWIDTH, amount, false, 0)
	if err != nil {
		return "", fmt.Errorf("failed to delegate bandwidth: %v", err)
	}
	tx := txExt.GetTransaction()
	if tx == nil {
		return "", fmt.Errorf("no raw transaction returned")
	}
	// sign raw transaction
	rawBytes, _ := proto.Marshal(tx.GetRawData())
	hash := sha256.Sum256(rawBytes)
	privKey, err := crypto.HexToECDSA(fromPrivate)
	if err != nil {
		return "", fmt.Errorf("invalid private key for signing: %v", err)
	}
	sig, err := crypto.Sign(hash[:], privKey)
	if err != nil {
		return "", fmt.Errorf("failed to sign delegate bandwidth tx: %v", err)
	}
	tx.Signature = [][]byte{sig}
	// broadcast signed transaction
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	ret, err := c.client.Client.BroadcastTransaction(ctx, tx)
	if err != nil {
		return "", fmt.Errorf("broadcast delegate bandwidth tx error: %v", err)
	}
	if !ret.GetResult() {
		return "", fmt.Errorf("delegate bandwidth tx rejected: %s", string(ret.GetMessage()))
	}
	txid := hex.EncodeToString(txExt.GetTxid())
	// poll
	for i := 0; i < 10; i++ {
		info, err := c.client.GetTransactionInfoByID(txid)
		if err == nil && info.GetResult() == coreproto.TransactionInfo_SUCESS {
			break
		}
		time.Sleep(10 * time.Second)
	}
	return txid, nil
}

// DelegateResourceEnergy 代理能量
func (c *Chain) DelegateResourceEnergy(fromPrivate, to string, amount int64) (string, error) {
	fromAddr, err := web3.GetTRONAddressFromPrivateKey(fromPrivate)
	if err != nil {
		return "", fmt.Errorf("invalid private key: %v", err)
	}
	// build transaction and optionally sign and broadcast
	txExt, err := c.client.DelegateResource(fromAddr, to, coreproto.ResourceCode_ENERGY, amount, false, 0)
	if err != nil {
		return "", fmt.Errorf("failed to delegate energy: %v", err)
	}
	tx := txExt.GetTransaction()
	if tx == nil {
		log.Warnf("DelegateResourceEnergy: no raw transaction returned, skipping sign & broadcast")
		return "", fmt.Errorf("no raw transaction returned")
	}
	rawBytes, _ := proto.Marshal(tx.GetRawData())
	hash := sha256.Sum256(rawBytes)
	privKey, err := crypto.HexToECDSA(fromPrivate)
	if err != nil {
		return "", fmt.Errorf("invalid private key for signing: %v", err)
	}
	sig, err := crypto.Sign(hash[:], privKey)
	if err != nil {
		return "", fmt.Errorf("failed to sign delegate energy tx: %v", err)
	}
	tx.Signature = [][]byte{sig}
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	ret, err := c.client.Client.BroadcastTransaction(ctx, tx)
	if err != nil {
		return "", fmt.Errorf("broadcast delegate energy tx error: %v", err)
	}
	if !ret.GetResult() {
		return "", fmt.Errorf("delegate energy tx rejected: %s", string(ret.GetMessage()))
	}

	txid := hex.EncodeToString(txExt.GetTxid())
	// poll
	for i := 0; i < 10; i++ {
		info, err := c.client.GetTransactionInfoByID(txid)
		if err == nil && info.GetResult() == coreproto.TransactionInfo_SUCESS {
			break
		}
		time.Sleep(10 * time.Second)
	}
	return txid, nil
}

// UnDelegateResourceBandwidth 回收代理带宽
func (c *Chain) UnDelegateResourceBandwidth(ownerPrivate, to string, amount int64) (string, error) {
	ownerAddr, err := web3.GetTRONAddressFromPrivateKey(ownerPrivate)
	if err != nil {
		return "", fmt.Errorf("invalid private key: %v", err)
	}
	// build transaction and optionally sign and broadcast
	txExt, err := c.client.UnDelegateResource(ownerAddr, to, coreproto.ResourceCode_BANDWIDTH, amount)
	if err != nil {
		return "", fmt.Errorf("failed to undelegate bandwidth: %v", err)
	}
	tx := txExt.GetTransaction()
	if tx == nil {
		return "", fmt.Errorf("no raw transaction returned")
	}
	rawBytes, _ := proto.Marshal(tx.GetRawData())
	hash := sha256.Sum256(rawBytes)
	privKey, err := crypto.HexToECDSA(ownerPrivate)
	if err != nil {
		return "", fmt.Errorf("invalid private key for signing: %v", err)
	}
	sig, err := crypto.Sign(hash[:], privKey)
	if err != nil {
		return "", fmt.Errorf("failed to sign undelegate bandwidth tx: %v", err)
	}
	tx.Signature = [][]byte{sig}
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	ret, err := c.client.Client.BroadcastTransaction(ctx, tx)
	if err != nil {
		return "", fmt.Errorf("broadcast undelegate bandwidth tx error: %v", err)
	}
	if !ret.GetResult() {
		return "", fmt.Errorf("undelegate bandwidth tx rejected: %s", string(ret.GetMessage()))
	}
	txid := hex.EncodeToString(txExt.GetTxid())
	// poll
	for i := 0; i < 10; i++ {
		info, err := c.client.GetTransactionInfoByID(txid)
		if err == nil && info.GetResult() == coreproto.TransactionInfo_SUCESS {
			break
		}
		time.Sleep(10 * time.Second)
	}
	return txid, nil
}

// UnDelegateResourceEnergy 回收代理能量
func (c *Chain) UnDelegateResourceEnergy(ownerPrivate, to string, amount int64) (string, error) {
	ownerAddr, err := web3.GetTRONAddressFromPrivateKey(ownerPrivate)
	if err != nil {
		return "", fmt.Errorf("invalid private key: %v", err)
	}
	// build transaction and optionally sign and broadcast
	txExt, err := c.client.UnDelegateResource(ownerAddr, to, coreproto.ResourceCode_ENERGY, amount)
	if err != nil {
		return "", fmt.Errorf("failed to undelegate energy: %v", err)
	}
	tx := txExt.GetTransaction()
	if tx == nil {
		return "", fmt.Errorf("no raw transaction returned")
	}
	rawBytes, _ := proto.Marshal(tx.GetRawData())
	hash := sha256.Sum256(rawBytes)
	privKey, err := crypto.HexToECDSA(ownerPrivate)
	if err != nil {
		return "", fmt.Errorf("invalid private key for signing: %v", err)
	}
	sig, err := crypto.Sign(hash[:], privKey)
	if err != nil {
		return "", fmt.Errorf("failed to sign undelegate energy tx: %v", err)
	}
	tx.Signature = [][]byte{sig}
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	ret, err := c.client.Client.BroadcastTransaction(ctx, tx)
	if err != nil {
		return "", fmt.Errorf("broadcast undelegate energy tx error: %v", err)
	}
	if !ret.GetResult() {
		return "", fmt.Errorf("undelegate energy tx rejected: %s", string(ret.GetMessage()))
	}
	txid := hex.EncodeToString(txExt.GetTxid())
	// poll
	for i := 0; i < 10; i++ {
		info, err := c.client.GetTransactionInfoByID(txid)
		if err == nil && info.GetResult() == coreproto.TransactionInfo_SUCESS {
			break
		}
		time.Sleep(10 * time.Second)
	}
	return txid, nil
}

func (c *Chain) WaitForTransaction(txID string, timeout, interval time.Duration) (bool, error) {
	deadline := time.Now().Add(timeout)
	for {
		// 查询交易信息
		txInfo, err := c.client.GetTransactionInfoByID(txID)
		if err != nil {
			// return false, fmt.Errorf("查询交易信息失败: %v", err)
		}
		if txInfo != nil && txInfo.Receipt != nil {
			if txInfo.Receipt.Result == coreproto.Transaction_Result_DEFAULT || txInfo.Receipt.Result == coreproto.Transaction_Result_SUCCESS {
				// 交易成功
				return true, nil
			} else {
				// 交易失败
				return false, fmt.Errorf("交易执行失败，原因: %s", txInfo.Receipt.GetResult())
			}
		}
		// 检查是否超时
		if time.Now().After(deadline) {
			return false, nil
		}
		// 等待下一次查询
		time.Sleep(interval)
	}
}
