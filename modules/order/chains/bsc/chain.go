package bsc

import (
	"context"
	"fmt"
	"math"
	"math/big"
	"s2/common/web3"
	"s2/modules/order/chains"
	"s2/modules/order/coins"
	"s2/modules/order/stypes"
	"s2/modules/order/wallet"
	"s2/pb"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/jfcwrlight/core/basic/events"
	"github.com/jfcwrlight/core/conf"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/utils"
)

var (
	walletKey         string // 生成用户充值地址私钥的key
	collectionAddress string // 归集转入地址
	gasPrivate        string // 归集转入手续费私钥
	withdrawPrivate   string // 提款手续费私钥
	gasAddress        string // 归集转入手续费地址
	scanDelay         uint64 // 归集延迟区块数量
	intervalBlock     uint64 //一次扫描区块数量  0 表示不扫描
)

// Chain BSC链实现
type Chain struct {
	clientRead  *ethclient.Client
	clientWrite *ethclient.Client
	native      *NativeToken
	contracts   map[common.Address]*BEP20Token // 扫描器列表
	lastBlock   uint64
	mu          sync.Mutex
	chainID     *big.Int // 链ID，用于签名
}

// New 创建BSC链实例
func New() (*Chain, error) {
	// 初始化配置
	walletKey = conf.Str("chain.walletKey", "")
	collectionAddress = strings.ToLower(conf.Str("chain.bsc.collectionAddress", ""))
	gasPrivate = conf.Str("chain.bsc.gasPrivate", "")
	withdrawPrivate = conf.Str("chain.bsc.withdrawPrivate", "")
	gasAddress_, _ := web3.GetAddressFromPrivateKey(gasPrivate)
	gasAddress = strings.ToLower(gasAddress_)
	scanDelay = uint64(conf.Num[int]("chain.bsc.delay", 10))
	intervalBlock = uint64(conf.Num[int]("chain.bsc.intervalblock", 0))
	rpcRead := conf.Str("chain.bsc.rpcURLRead", "")
	rpcWrite := conf.Str("chain.bsc.rpcURLWrite", "")
	// 连接BSC节点
	clientRead, err := ethclient.Dial(rpcRead)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to BSC: %v", err)
	}
	clientWrite, err := ethclient.Dial(rpcWrite)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to BSC: %v", err)
	}
	chain := &Chain{
		clientRead:  clientRead,
		clientWrite: clientWrite,
		contracts:   make(map[common.Address]*BEP20Token),
	}
	// 获取链ID，用于签名器
	cid, err := clientRead.NetworkID(context.Background())
	if err != nil {
		return nil, fmt.Errorf("failed to fetch chain ID: %v", err)
	}
	chain.chainID = cid
	// 创建原生代币实例
	chain.native = NewNativeToken(chain)
	// 原生代币也具有需要充值估计，有代币的属性
	coins.Reg(chain.native)
	// 创建归集实例
	// collection := NewCollection(client, native, usdt)

	// 创建代币实例
	usdt := conf.Str("chain.bsc.USDT.address", "")
	if usdt != "" {
		collectionLimit := conf.Num[float64]("chain.bsc.USDT.collectionLimit", 0.99)
		rechargeOpen := conf.Bool("chain.bsc.USDT.rechargeOpen", false)
		withdrawOpen := conf.Bool("chain.bsc.USDT.withdrawOpen", false)
		usdt := NewBEP20Token(chain, usdt, pb.BSCUSDT, 1e18, pb.USDT, collectionLimit, rechargeOpen, withdrawOpen, pb.CoinConfig{
			AssetId:      int64(conf.Num[int]("chain.bsc.USDT.AssetId", 0)),
			ExchangeRate: conf.Num[float64]("chain.bsc.USDT.AssetExchangeRate", 0),
			Fees:         conf.Num[float64]("chain.bsc.USDT.Fees", 0),
			MinWithdraw:  conf.Num[float64]("chain.bsc.USDT.MinWithdraw", 0),
			MinRecharge:  conf.Num[float64]("chain.bsc.USDT.MinRecharge", 0),
		})
		chain.contracts[usdt.contractAddress] = usdt
		coins.Reg(usdt)
	}

	lastBlock, err := chains.GetLastScanInfo(chain.Type().String())
	if err != nil {
		return nil, fmt.Errorf("failed to get latest scanned block: %w", err)
	}
	if lastBlock != "" {
		n, err := strconv.Atoi(lastBlock)
		if err != nil {
			log.Error(err)
			return nil, err
		}
		chain.lastBlock = uint64(n)
	} else { //第一次启动最新的区块为上一次区块
		chain.lastBlock, err = clientRead.BlockNumber(context.Background())
		if err != nil {
			return nil, fmt.Errorf("failed to get latest block number: %w", err)
		}
		lastBlock = strconv.Itoa(int(chain.lastBlock))
		// 保存最新区块号
		if err := chains.SaveLatestScanInfo(chain.Type().String(), lastBlock); err != nil {
			return nil, fmt.Errorf("failed to save latest block: %w", err)
		}
	}

	// 注册链
	chains.Reg(chain)

	return chain, nil
}

func (e *Chain) ClientRead() *ethclient.Client {
	return e.clientRead
}

func (e *Chain) ClientWrite() *ethclient.Client {
	return e.clientWrite
}

func (e *Chain) ClientId() *big.Int {
	return e.chainID
}

func (e *Chain) Type() pb.EnumChain {
	return pb.CHAIN_BSC
}

// GetUserRechargeAddress 获取用户充值地址
func (c *Chain) GetUserRechargeAddress(userID int64) (address string, extInfo string, err error) {
	// 从数据库获取已存在的地址
	wa, err := wallet.GetRechargeAddress(userID, c)
	if err != nil {
		log.Error(err)
		return "", "", err
	}
	if wa != nil {
		return wa.Address, "", nil
	}

	// 生成新的私钥和地址
	private, err := c.GetUserRechargePrivate(userID)
	if err != nil {
		log.Error(err)
		return "", "", err
	}
	address, _ = web3.GetAddressFromPrivateKey(private)
	address = strings.ToLower(address)

	// 保存到数据库
	err = wallet.SaveRechargeAddress(userID, c, address, "")
	if err != nil {
		log.Error(err)
		return
	}
	return address, "", nil
}

// GetUserRechargePrivate 生成用户充值私钥
func (c *Chain) GetUserRechargePrivate(userID int64) (private string, err error) {
	in := walletKey + strconv.Itoa(int(userID))
	key := in[:16]
	x := in[len(in)-16:]
	privatekey := web3.StringToKey256([]byte(key), []byte(x), []byte(in), 1)
	return privatekey, nil
}

// StartScan 实现 IChain 接口的 StartScan 方法
func (e *Chain) StartScan(ctx context.Context) error {
	// 获取起始区块号
	ticker := time.NewTicker(time.Second * 3)
	// 扫描循环
	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-ticker.C:
			e.doScan(ctx)
		}
	}
}

// StartScan 开始扫描
func (c *Chain) doScan(ctx context.Context) error {
	defer utils.RecoverPanic()
	// 调试模式指定区块
	debugBlockNumber := conf.Num[int]("chain.bsc.debugblocknumber", 0)
	if debugBlockNumber > 0 {
		c.scanBlock(uint64(debugBlockNumber), uint64(debugBlockNumber))
		return nil
	}
	for {
		if intervalBlock == 0 {
			return nil
		}
		// 获取最新区块
		block, err := c.clientRead.BlockNumber(ctx)
		if err != nil {
			log.Errorf("failed to get block number: %v", err)
			time.Sleep(time.Second)
			continue
		}
		from := c.lastBlock + 1
		end := block - scanDelay
		if end < from {
			time.Sleep(time.Second * 3)
			continue
		}
		for i := from; i <= end; {
			to := i + (intervalBlock - 1)
			if to > end {
				to = end
			}
			if utils.ContextDone(ctx) {
				return ctx.Err()
			}
			if block-c.lastBlock > 30 {
				log.Warnf("扫描区块延迟太大: %d", block-c.lastBlock)
			}
			err := c.scanBlock(i, to)
			if err != nil {
				log.Errorf("failed to scan block %d: %v", i, err)
				continue
			}
			c.lastBlock = to
			i = to + 1
			if err := chains.SaveLatestScanInfo(c.Type().String(), strconv.Itoa(int(c.lastBlock))); err != nil {
				log.Errorf("failed to save latest block: %w", err)
				time.Sleep(time.Second)
				continue
			}
		}
	}
}

// scanBlock 扫描单个区块，解析 BNB 和 BEP20 转账 要过滤pending，失败的交易
func (c *Chain) scanBlock(fromBlockNumber, toBlockNumber uint64) error {
	defer utils.RecoverPanic()
	if c.native.RechargeOpen() {
		for i := fromBlockNumber; i <= toBlockNumber; i++ {
			err := c.scanBlockNative(i)
			if err != nil {
				log.Error(err)
				return err
			}
		}
	}
	err := c.scanBlockToken(fromBlockNumber, toBlockNumber)
	if err != nil {
		log.Error(err)
		return err
	}
	return nil
}
func (c *Chain) scanBlockToken(fromBlockNumber, toBlockNumber uint64) error {
	// 获取区块的日志（包含所有事件）
	query := ethereum.FilterQuery{
		FromBlock: new(big.Int).SetUint64(fromBlockNumber),
		ToBlock:   new(big.Int).SetUint64(toBlockNumber),
	}
	for address, contract := range c.contracts {
		query.Addresses = append(query.Addresses, address)
		query.Topics = append(query.Topics, contract.Topics())
	}
	// 收集所有日志
	logs, err := c.clientRead.FilterLogs(context.Background(), query)
	if err != nil {
		log.Error(err)
		return err
	}
	for _, item := range logs {
		utils.ExecAndRecover(func() {
			event := c.contracts[item.Address].ParseLog(item)
			if event == nil {
				return
			}
			from := strings.ToLower(event.From.Hex())
			to := strings.ToLower(event.To.Hex())
			exist, _, err := wallet.ContainsAddress(c.Type(), to)
			if err != nil {
				log.Error(err)
				return
			}
			if !exist {
				return
			}
			coinSymbol := c.contracts[item.Address].CoinSymbol()
			events.Publish(stypes.EventTransfer{
				TxHash:    item.TxHash.Hex(),
				Coin:      coinSymbol,
				From:      from,
				To:        to,
				Value:     event.Value,
				Timestamp: uint64(time.Now().Unix()),
				Chain:     c.Type(),
			})
		})
	}
	return nil
}
func (c *Chain) scanBlockNative(blockNumber uint64) error {
	defer utils.RecoverPanic()
	block, err := c.clientRead.BlockByNumber(context.Background(), big.NewInt(int64(blockNumber)))
	if err != nil {
		return fmt.Errorf("failed to get block: %v", err)
	}
	// 扫描原生转账
	for _, tx := range block.Transactions() {
		if tx.Value().Sign() <= 0 { // 仅处理有BNB转账的交易
			continue
		}
		to_ := tx.To()
		if to_ == nil {
			continue
		}
		signer := types.LatestSignerForChainID(c.chainID)
		from_, err := types.Sender(signer, tx)
		if err != nil {
			log.Error(err)
			continue
		}
		from := strings.ToLower(from_.Hex())
		// 跳过归集转入手续费
		if from == gasAddress {
			continue
		}
		to := strings.ToLower(to_.Hex())
		exist, _, err := wallet.ContainsAddress(c.Type(), to)
		if err != nil {
			log.Error(err)
			continue
		}
		if !exist {
			continue
		}
		receipt, err := c.clientRead.TransactionReceipt(context.Background(), tx.Hash())
		if err != nil {
			log.Error(err)
			continue
		}
		if receipt.Status != 1 {
			continue
		}
		events.Publish(stypes.EventTransfer{
			TxHash:    tx.Hash().Hex(),
			Coin:      c.native.CoinSymbol(),
			From:      from,
			To:        to,
			Value:     tx.Value(),
			Timestamp: uint64(time.Now().Unix()),
			Chain:     c.Type(),
		})
	}
	return nil
}

func (c *Chain) IsOfficialAddress(address string) bool {
	address = strings.ToLower(address)
	return address == gasAddress || address == collectionAddress
}

// Collection 归集
func (c *Chain) Collection(coin stypes.ICoin, private string) (*big.Int, error) {
	c.mu.Lock()
	defer c.mu.Unlock()
	if coin.CoinSymbol() == c.native.CoinSymbol() {
		return c.CollectionNative(private)
	}
	return c.CollectionToken(coin, private)
}

func (c *Chain) CollectionToken(coin stypes.ICoin, private string) (*big.Int, error) {
	address, err := web3.GetAddressFromPrivateKey(private)
	if err != nil {
		return nil, fmt.Errorf("failed to get address from private key: %v", err)
	}
	address = strings.ToLower(address)
	token, ok := coins.Get(coin.CoinSymbol())
	if !ok {
		return nil, fmt.Errorf("coin is not a token")
	}
	bep20, ok := token.(*BEP20Token)
	if !ok {
		return nil, fmt.Errorf("coin is not a bep20 token")
	}
	allowance, err := bep20.Allowance(address, gasAddress)
	if err != nil {
		return nil, fmt.Errorf("failed to get allowance: %v", err)
	}
	if allowance.Sign() <= 0 {
		gas, err := bep20.ApproveEstimateGas(private, gasAddress, big.NewInt(math.MaxInt64))
		if err != nil {
			return nil, fmt.Errorf("failed to approve token: %v", err)
		}
		gasPrice, err := c.native.GasPrice()
		if err != nil {
			return nil, fmt.Errorf("failed to get gas price: %v", err)
		}
		useGas := new(big.Int).Mul(big.NewInt(int64(gas)), gasPrice)
		useGas.Mul(useGas, big.NewInt(2))
		amount, err := c.native.Balance(address)
		if err != nil {
			return nil, fmt.Errorf("failed to get balance: %v", err)
		}
		if amount.Cmp(useGas) < 0 {
			_, err = c.native.Transfer(gasPrivate, address, useGas)
			if err != nil {
				return nil, fmt.Errorf("failed to transfer token: %v", err)
			}
			time.Sleep(time.Second * 6)
		}
		_, err = bep20.Approve(private, gasAddress, big.NewInt(math.MaxInt64))
		if err != nil {
			return nil, fmt.Errorf("failed to approve token: %v", err)
		}
		time.Sleep(time.Second * 6)
	}
	amount, err := bep20.Balance(address)
	if err != nil {
		return nil, fmt.Errorf("failed to get balance: %v", err)
	}
	amountFlot, _ := amount.Float64()
	a := amountFlot / float64(bep20.CoinDecimals())
	if a < bep20.CollectionLimit() {
		return nil, fmt.Errorf("balance is not enough amount: %s, limit: %f", amount.String(), bep20.CollectionLimit())
	}
	_, err = bep20.TransferFrom(gasPrivate, address, collectionAddress, amount)
	if err != nil {
		return nil, fmt.Errorf("failed to transfer token: %v", err)
	}
	return amount, nil
}
func (c *Chain) CollectionNative(address string) (*big.Int, error) {
	return nil, nil
}

// WithdrawOrder 实现提款功能
func (c *Chain) WithdrawOrder(amount float64, coin stypes.ICoin, address string) error {
	if !web3.IsValidBSCAddress(address) {
		return fmt.Errorf("invalid address: %s", address)
	}
	// 检查地址格式
	if !common.IsHexAddress(address) {
		return fmt.Errorf("invalid address format: %s", address)
	}
	// 获取代币余额
	balance, err := coin.Balance(gasAddress)
	if err != nil {
		return fmt.Errorf("failed to get balance: %v", err)
	}
	// 计算提款金额（转换为最小单位）
	withdrawAmount := new(big.Int)
	withdrawAmount.SetInt64(int64(amount * float64(coin.CoinDecimals())))

	// 检查余额是否足够
	if balance.Cmp(withdrawAmount) < 0 {
		return fmt.Errorf("insufficient balance: have %s, need %s", balance.String(), withdrawAmount.String())
	}
	// 执行转账
	txHash, err := coin.Transfer(withdrawPrivate, address, withdrawAmount)
	if err != nil {
		return fmt.Errorf("BSC failed to transfer: %v", err)
	}
	log.Infof("BSC Withdraw success: amount=%f, coin=%s, address=%s, txHash=%s",
		amount, coin.CoinSymbol().String(), address, txHash)
	return nil
}
