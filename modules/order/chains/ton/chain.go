package ton

import (
	"bytes"
	"context"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"math/big"
	"strconv"
	"sync"
	"time"

	"s2/modules/order/chains"
	"s2/modules/order/coins"
	"s2/modules/order/stypes"
	"s2/pb"

	"github.com/jfcwrlight/core/basic/events"
	"github.com/jfcwrlight/core/conf"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/utils"
	"github.com/xssnick/tonutils-go/address"
	"github.com/xssnick/tonutils-go/liteclient"
	"github.com/xssnick/tonutils-go/tlb"
	"github.com/xssnick/tonutils-go/ton"
	"github.com/xssnick/tonutils-go/tvm/cell"
)

var (
	walletKey              string
	collectionAddress      string
	collectionAddressParse *address.Address
	withdrawWords          string
	intervalBlock          uint64
)

// TIP-3 转账方法 ID，用于解析合约调用
const tip3TransferMethodID = 0xf8a7ea5

// Chain TON 链实现
// TODO: 补充 SDK 调用和区块扫描实现细节
// TODO: 注册 TIP-3 代币到 contracts
// TODO: 完善归集、提款和用户充值地址逻辑

// Chain 定义 TON 链实例
// client: TON API 客户端
// native: 原生 TON Token
// contracts: TIP-3 代币列表
// lastBlock: 上次扫描的 masterchain 序号
// mu: 并发保护锁

type Chain struct {
	client    ton.APIClientWrapped
	native    *NativeToken
	contracts map[string]*JettonToken
	lastTxs   map[string]*TonScanCursor
	mu        sync.Mutex
}

// TonScanCursor 用于持久化 TIP-3 交易分页游标
type TonScanCursor struct {
	LT   uint64 `json:"lt"`
	Hash []byte `json:"hash"`
}

// New 创建 TON 链实例
func New() (*Chain, error) {
	// 初始化配置
	walletKey = conf.Str("chain.walletKey", "")
	collectionAddress = conf.Str("chain.ton.collectionAddress", "")
	collectionAddressParse = address.MustParseAddr(collectionAddress)
	withdrawWords = conf.Str("chain.ton.withdrawWords", "")
	intervalBlock = uint64(conf.Num[int]("chain.ton.intervalblock", 0))
	// 连接 TON 节点
	configURL := conf.Str("chain.ton.rpcURL", "")
	pool := liteclient.NewConnectionPool()
	if err := pool.AddConnectionsFromConfigUrl(context.Background(), configURL); err != nil {
		return nil, fmt.Errorf("failed to connect to TON network: %v", err)
	}
	cli := ton.NewAPIClient(pool)
	chain := &Chain{
		client:    cli.WithRetry(),
		contracts: make(map[string]*JettonToken),
	}
	// 原生代币实例
	chain.native = NewNativeToken(cli, chain)
	coins.Reg(chain.native)

	// 注册 TIP-3 代币
	tokenAddr := conf.Str("chain.ton.USDT.address", "")
	if tokenAddr != "" {
		// USDT的精度是1e6 主币精度是1e9
		rechargeOpen := conf.Bool("chain.ton.USDT.rechargeOpen", false)
		withdrawOpen := conf.Bool("chain.ton.USDT.withdrawOpen", false)
		jt := NewJettonToken(cli, chain, tokenAddr, pb.TONUSDT, 1e6, pb.USDT, 0, rechargeOpen, withdrawOpen, pb.CoinConfig{
			AssetId:      int64(conf.Num[int]("chain.ton.USDT.AssetId", 0)),
			ExchangeRate: conf.Num[float64]("chain.ton.USDT.AssetExchangeRate", 0),
			Fees:         conf.Num[float64]("chain.ton.USDT.Fees", 0),
			MinWithdraw:  conf.Num[float64]("chain.ton.USDT.MinWithdraw", 0),
			MinRecharge:  conf.Num[float64]("chain.ton.USDT.MinRecharge", 0),
		})
		chain.contracts[tokenAddr] = jt
		coins.Reg(jt)
	}
	master, err := cli.CurrentMasterchainInfo(context.Background())
	if err != nil {
		log.Error(err)
		return nil, fmt.Errorf("failed to get latest block: %w", err)
	}
	// 预先计算每个 JettonToken 对应的归集钱包地址
	for _, jt := range chain.contracts {
		var er *ton.ExecutionResult
		var err error
		if jt.walletAddress == nil {
			for i := 0; i < 3; i++ {
				ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
				defer cancel()
				er, err = chain.client.RunGetMethod(
					ctx, master,
					address.MustParseAddr(jt.contractAddress),
					"get_wallet_address",
					cell.BeginCell().MustStoreAddr(collectionAddressParse).EndCell().BeginParse(),
				)
				if err == nil {
					break
				}
				log.Warnf("get_wallet_address %s 重试第 %d 次失败：%v", jt.contractAddress, i+1, err)
				time.Sleep(time.Duration(i+1) * time.Second)
			}
			if err != nil {
				return nil, fmt.Errorf("从 %s 获取钱包地址失败（重试 3 次后）：%w", jt.contractAddress, err)
			}
			sl, err := er.Slice(0)
			if err != nil {
				return nil, fmt.Errorf("failed to get wallet address from %s: %v", jt.contractAddress, err)
			}
			wa, err := sl.LoadAddr()
			if err != nil {
				return nil, fmt.Errorf("failed to get wallet address from %s: %v", jt.contractAddress, err)
			}
			jt.walletAddress = wa
		}
	}

	lastTxStr, err := chains.GetLastScanInfo(chain.Type().String())
	if err != nil {
		log.Error(err)
		return nil, fmt.Errorf("failed to get last block number: %w", err)
	}
	if lastTxStr != "" {
		lastTxs := map[string]*TonScanCursor{}
		err = json.Unmarshal([]byte(lastTxStr), &lastTxs)
		if err != nil {
			log.Error(err)
			return nil, fmt.Errorf("failed to get latest block: %w", err)
		}
		chain.lastTxs = lastTxs
	} else {
		lastTxs := map[string]*TonScanCursor{}
		account, err := cli.WaitForBlock(master.SeqNo).GetAccount(context.Background(), master, collectionAddressParse)
		if err != nil {
			log.Error(err)
			return nil, fmt.Errorf("failed to get latest block: %w", err)
		}
		if chain.native.RechargeOpen() {
			lastTxs[chain.native.CoinSymbol().String()] = &TonScanCursor{
				LT:   account.LastTxLT,
				Hash: account.LastTxHash,
			}
		}
		for _, jt := range chain.contracts {
			account, err := cli.WaitForBlock(master.SeqNo).GetAccount(context.Background(), master, collectionAddressParse)
			if err != nil {
				log.Error(err)
				return nil, fmt.Errorf("failed to get latest block: %w", err)
			}
			lastTxs[jt.CoinSymbol().String()] = &TonScanCursor{
				LT:   account.LastTxLT,
				Hash: account.LastTxHash,
			}
		}
		chain.lastTxs = lastTxs
		b, _ := json.Marshal(lastTxs)
		err = chains.SaveLatestScanInfo(chain.Type().String(), string(b))
		if err != nil {
			log.Error(err)
			return nil, fmt.Errorf("failed to save latest scan info: %w", err)
		}
	}
	chains.Reg(chain)
	return chain, nil
}

// Type 返回链类型
func (c *Chain) Type() pb.EnumChain {
	return pb.CHAIN_TON
}

// GetUserRechargeAddress 获取用户充值地址
func (c *Chain) GetUserRechargeAddress(userID int64) (string, string, error) {
	// For TON, use a single deposit address and pass userID as memo; no per-user key needed
	if collectionAddress == "" {
		return "", "", fmt.Errorf("TON deposit address is not configured")
	}
	return collectionAddress, strconv.Itoa(int(userID)), nil
}

// GetUserRechargePrivate 生成用户充值私钥
func (c *Chain) GetUserRechargePrivate(userID int64) (string, error) {
	// No per-user private key; funds are received on the single deposit address
	return "", nil
}

func (c *Chain) StartScan(ctx context.Context) error {
	if intervalBlock == 0 {
		return nil
	}
	ticker := time.NewTicker(time.Second * 60)
	defer ticker.Stop()
	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-ticker.C:
			c.scanBlocks(ctx)
		}
	}
}

func (c *Chain) scanBlocks(ctx context.Context) {

	// 分别扫描原生 TON 和 TIP-3 代币
	if c.native.RechargeOpen() {
		c.scanBlocksNative(ctx)
	}
	c.scanBlocksJetton(ctx)
}

// scanBlocksNative 专门处理原生 TON 充值
func (c *Chain) scanBlocksNative(ctx context.Context) {
	defer utils.RecoverPanic()
	lastTx := c.lastTxs[c.native.CoinSymbol().String()]
	master, err := c.client.CurrentMasterchainInfo(ctx)
	if err != nil {
		log.Error(err)
		return
	}
	// account, err := c.client.WaitForBlock(master.SeqNo).GetAccount(ctx, master, collectionAddressParse)
	account, err := c.client.GetAccount(ctx, master, collectionAddressParse)
	if err != nil {
		log.Error(err)
		return
	}
	const numLimit = 15
	var (
		txsALL  []*tlb.Transaction
		curHash []byte = account.LastTxHash
		curLT   uint64 = account.LastTxLT
	)
	for {
		if utils.ContextDone(ctx) {
			return
		}
		txs, err := c.client.ListTransactions(ctx, collectionAddressParse, numLimit, curLT, curHash)
		if err == ton.ErrNoTransactionsWereFound {
			return
		}
		if err != nil {
			log.Error(err)
			return
		}
		if len(txs) == 0 {
			return
		}
		curHash = txs[0].PrevTxHash
		curLT = txs[0].PrevTxLT
		for _, tx := range txs {
			// 跳过所有 LT 小于上次游标的，或 LT 相同且 Hash 相同的
			if tx.LT < lastTx.LT ||
				(tx.LT == lastTx.LT && bytes.Equal(tx.Hash, lastTx.Hash)) {
				continue
			}
			txsALL = append(txsALL, tx)
			log.Infof("tron scanBlocksNative tx: %s, lt: %d lastTx.Hash: %s lastTx.LT: %d", hex.EncodeToString(tx.Hash), tx.LT, hex.EncodeToString(lastTx.Hash), lastTx.LT)
		}
		if len(txs) < numLimit {
			break
		}
	}
	for _, tx := range txsALL {
		if tx.IO.In == nil || tx.IO.In.Msg == nil {
			continue
		}
		var (
			value    *big.Int
			body     *cell.Cell
			fromAddr *address.Address
		)
		txHash := hex.EncodeToString(tx.Hash)
		switch m := tx.IO.In.Msg.(type) {
		case *tlb.InternalMessage:
			// 用户链外→链内的那笔（充值）
			value = m.Amount.Nano()
			body = m.Body
			fromAddr = m.SrcAddr
		case *tlb.ExternalMessage:
			// 合约内部转账（一般不含 memo）
			value = m.ImportFee.Nano()
			body = m.Body
			fromAddr = m.SrcAddr
		default:
			continue
		}
		// 如果真的没有金额，跳过
		//似乎是TON交易必须有一笔很小的费用用来通知忽略
		if value.Cmp(big.NewInt(1000000)) <= 0 {
			continue
		}
		// 解析评论：跳过 32 位 opcode（=0），然后 loadSnake
		userIDStr := ""
		if body != nil {
			p := body.BeginParse()
			if op, err := p.LoadUInt(32); err == nil && op == 0 {
				if s, err := p.LoadStringSnake(); err == nil {
					userIDStr = s
				}
			}
		}
		from := fromAddr.Bounce(false).String()
		userID, err := strconv.Atoi(userIDStr)
		if err != nil {
			log.Warnf("ton scanBlocks tx parse userID error, %s, %s", userIDStr, txHash)
			continue
		}
		if value.Cmp(big.NewInt(0)) > 0 {
			events.Publish(stypes.TONEventTransfer{
				TxHash:    txHash,
				Coin:      c.native.CoinSymbol(),
				From:      from,
				Uid:       int64(userID),
				Value:     value,
				Timestamp: uint64(time.Now().Unix()),
				Chain:     c.Type(),
			})
		}
	}
	c.lastTxs[c.native.CoinSymbol().String()] = &TonScanCursor{
		LT:   account.LastTxLT,
		Hash: account.LastTxHash,
	}
	b, _ := json.Marshal(c.lastTxs)
	err = chains.SaveLatestScanInfo(c.Type().String(), string(b))
	if err != nil {
		log.Error(err)
		return
	}
}

// scanBlocksJetton 专门处理 TIP-3 代币充值
func (c *Chain) scanBlocksJetton(ctx context.Context) {
	defer utils.RecoverPanic()
	const numLimit = 15
	master, _ := c.client.CurrentMasterchainInfo(ctx)
	for _, jt := range c.contracts {
		lastTx := c.lastTxs[jt.CoinSymbol().String()]
		// account, _ := c.client.WaitForBlock(master.SeqNo).GetAccount(ctx, master, jt.walletAddress)
		account, _ := c.client.GetAccount(ctx, master, collectionAddressParse)
		curLT, curHash := account.LastTxLT, account.LastTxHash
		if lastTx == nil {
			lastTx = &TonScanCursor{
				LT:   curLT,
				Hash: curHash,
			}
		}
		// 2. 分页翻页获取新交易
		var all []*tlb.Transaction
		for {
			if utils.ContextDone(ctx) {
				return
			}
			txs, err := c.client.ListTransactions(ctx, collectionAddressParse, numLimit, curLT, curHash)
			if err == ton.ErrNoTransactionsWereFound {
				break
			}
			if err != nil || len(txs) == 0 {
				break
			}
			// 收集所有 "更新的" 交易（LT > 上次游标）
			for _, tx := range txs {
				if tx.LT < lastTx.LT ||
					(tx.LT == lastTx.LT && bytes.Equal(tx.Hash, lastTx.Hash)) {
					continue
				}
				all = append(all, tx)
				log.Infof("tx: %s, lt: %d lastTx: %d lastTx.LT: %d", tx.Hash, tx.LT, lastTx.LT, lastTx.LT)
			}
			// 翻页游标：仍往旧方向
			curLT, curHash = txs[0].PrevTxLT, txs[0].PrevTxHash
			if len(txs) < numLimit {
				break
			}
		}
		// 3. 倒序发布
		for i := len(all) - 1; i >= 0; i-- {
			tx := all[i]
			if tx.IO.In.MsgType != tlb.MsgTypeInternal {
				continue
			}
			msg := tx.IO.In.Msg.(*tlb.InternalMessage)
			from := msg.SenderAddr()
			fromBounced := from.Bounce(false).String()
			jsBounced := jt.walletAddress.Bounce(false).String()
			log.Infof("jsBounced: %s", jsBounced)
			if fromBounced != jsBounced { //暂时理解为 先转到内部地址jsBounced然后转给目标地址
				continue
			}
			log.Info("fromBounced:", fromBounced) //UQCD_p4hYFzn8OHlCfra95Z7JrvUM4Zvkjl2vYhZK1T7YD9S
			internal := tx.IO.In.AsInternal()
			if internal.Body == nil {
				continue
			}
			hash := hex.EncodeToString(tx.Hash)
			parser := internal.Body.BeginParse()
			// 1) 跳过 methodID、queryID、amount、destination、response_destination
			// parser.LoadUInt(32)
			if id, err := parser.LoadUInt(32); err != nil || id != tip3TransferMethodID {
				// log.Warnf("ton scanBlocks tx parse methodID error, %s, %d", hash, id)
				// continue
			}
			parser.LoadUInt(64)
			amount, err := parser.LoadBigCoins()
			if err != nil {
				continue
			}
			origin, err := parser.LoadAddr() // 原始发送方地址
			if err != nil {
				continue
			}
			originBounced := origin.Bounce(false).String()
			log.Infof("originBounced: %s", originBounced)
			parser.LoadAddr() // response_destination
			// 2) custom_payload
			cpl, _ := parser.LoadMaybeRef()
			var memo string
			if cpl != nil {
				p2 := cpl
				if op, err := p2.LoadUInt(32); err == nil && op == 0 {
					memo, _ = p2.LoadStringSnake()
				}
			}
			/*
				if id, err := parser.LoadUInt(32); err != nil || id != tip3TransferMethodID {
					log.Warnf("ton scanBlocks tx parse methodID error, %s, %d", hash, id)
					// continue
				}
				parser.LoadUInt(64)
				amount, err := parser.LoadBigCoins()
				if err != nil {
					continue
				}
				from := msg.SenderAddr()
				fromBounced := from.Bounce(false).String()
				log.Info("fromBounced:", fromBounced) //UQCD_p4hYFzn8OHlCfra95Z7JrvUM4Zvkjl2vYhZK1T7YD9S
				dst, err := parser.LoadAddr()
				if err != nil {
					continue
				}
				dstBounced := dst.Bounce(false).String()
				log.Infof("dstBounced: %s", dstBounced)
				if dstBounced != collectionAddress {
					// continue
				}
				// 或者简洁地直接比较底层的账户 ID，而不看 bounce flag：
				target := address.MustParseAddr(collectionAddress)
				if !bytes.Equal(dst.Data(), target.Data()) || dst.Workchain() != target.Workchain() {
					// continue
				}
				userIDStr := internal.Comment()
				if userIDStr == "" {
					// 检查传入消息
					if tx.IO.In != nil && tx.IO.In.Msg != nil {
						internal := tx.IO.In.AsInternal()
						if internal.Body != nil {
							// parser := internal.Body.BeginParse()
							// 尝试从消息体中提取评论
							comment, err := c.extractComment(internal.Body)
							// comment, err := parser.LoadStringSnake()
							if err == nil && comment != "" {
								fmt.Printf("交易 LT: %d, 传入评论: %s\n", tx.LT, comment)
								userIDStr = comment
							}
						}
					}
				}
				if userIDStr == "" {
					// 检查传出消息
					if tx.IO.Out != nil && tx.IO.Out.List != nil {
						ls, err := tx.IO.Out.ToSlice()
						if err == nil {
							for _, out := range ls {
								internal := out.AsInternal()
								if internal.Body != nil {
									// parser := internal.Body.BeginParse()
									comment, err := c.extractComment(internal.Body)
									// 尝试从消息体中提取评论
									// comment, err := c.extractComment(parser)
									// comment, err := parser.LoadStringSnake()
									if err == nil && comment != "" {
										fmt.Printf("交易 LT: %d, 传出评论: %s\n", tx.LT, comment)
										userIDStr = comment
									}
								}
							}
						}
					}
				}
			*/
			userID, err := strconv.Atoi(memo)
			if err != nil {
				log.Warnf("ton scanBlocks tx parse userID error, %s, %s", memo, hash)
				continue
			}
			if amount.Cmp(big.NewInt(0)) > 0 {
				events.Publish(stypes.TONEventTransfer{
					TxHash:    hash,
					Coin:      jt.CoinSymbol(),
					From:      originBounced,
					Uid:       int64(userID),
					Value:     amount,
					Timestamp: uint64(time.Now().Unix()),
					Chain:     c.Type(),
				})
			}
		}

		// 4. 保存最新游标
		c.lastTxs[jt.CoinSymbol().String()] = &TonScanCursor{
			LT:   account.LastTxLT,
			Hash: account.LastTxHash,
		}
		b, _ := json.Marshal(c.lastTxs)
		err := chains.SaveLatestScanInfo(c.Type().String(), string(b))
		if err != nil {
			log.Error(err)
			return
		}
	}
}
func (c *Chain) extractComment(body *cell.Cell) (string, error) {
	p := body.BeginParse()
	// skip: methodID (32), queryID (64), amount, dstAddr, respAddr
	if _, err := p.LoadUInt(32); err != nil {
		return "", err
	}
	if _, err := p.LoadUInt(64); err != nil {
		return "", err
	}
	if _, err := p.LoadBigCoins(); err != nil {
		return "", err
	}
	if _, err := p.LoadAddr(); err != nil {
		return "", err
	}
	if _, err := p.LoadAddr(); err != nil {
		return "", err
	}
	// skip optional custom payload
	if _, err := p.LoadMaybeRef(); err != nil {
		return "", err
	}
	// skip forward ton amount
	if _, err := p.LoadBigCoins(); err != nil {
		return "", err
	}
	// grab the forward payload cell
	fwd, err := p.LoadMaybeRef()
	if err != nil || fwd == nil {
		return "", fmt.Errorf("no forward_payload")
	}

	// now inside the forward payload the same 0+snake‐string pattern applies
	sp := fwd
	if code, err := sp.LoadUInt(32); err != nil || code != 0 {
		return "", fmt.Errorf("not a text payload")
	}
	return sp.LoadStringSnake()
}

// Collection 归集资产
func (c *Chain) Collection(coin stypes.ICoin, private string) (*big.Int, error) {
	return nil, nil
}

// WithdrawOrder 执行提款
func (c *Chain) WithdrawOrder(amount float64, coin stypes.ICoin, address string) error {
	withdrawAmount := new(big.Int)
	withdrawAmount.SetInt64(int64(amount * float64(coin.CoinDecimals())))
	txHash, err := coin.Transfer(withdrawWords, address, withdrawAmount)
	if err != nil {
		return fmt.Errorf("TON failed to transfer: %v", err)
	}
	log.Infof("TON Withdraw success: amount=%f, coin=%s, address=%s, txHash=%s",
		amount, coin.CoinSymbol().String(), address, txHash)
	return nil
}

// IsOfficialAddress 官方地址判断
func (c *Chain) IsOfficialAddress(address string) bool {
	return address == collectionAddress
}
