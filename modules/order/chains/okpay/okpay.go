package okpay

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"math/big"
	"net/http"
	"net/url"
	"s2/modules/order/chains"
	"s2/modules/order/coins"
	"s2/modules/order/stypes"
	"s2/pb"
	"sort"
	"strings"
	"time"

	"github.com/jfcwrlight/core/conf"
	"github.com/jfcwrlight/core/log"
)

type OkPay struct {
	ID                       string
	Token                    string
	ApiUrlPayLink            string
	ApiUrlTransfer           string
	ApiUrlTransactionHistory string
	url                      string
}

type NotifyData struct {
	OrderID   string `json:"order_id"`
	UniqueID  string `json:"unique_id"`
	PayUserID string `json:"pay_user_id"`
	Amount    string `json:"amount"`
	Coin      string `json:"coin"`
	Status    string `json:"status"`
	Code      string `json:"code"`
	Sign      string `json:"sign"`
}

// Chain BSC链实现
type Chain struct {
	client     *OkPay
	return_url string
	name       string
}

func New() (*Chain, error) {
	appid := conf.Str("chain.okpay.appid")
	token := conf.Str("chain.okpay.token")
	chain := &Chain{}
	client := NewOkayPay(appid, token)
	chain.client = client
	chain.return_url = conf.Str("chain.okpay.return_url")
	chain.name = conf.Str("chain.okpay.name")
	// 创建代币实例
	rechargeOpen := conf.Bool("chain.okpay.USDT.rechargeOpen", false)
	withdrawOpen := conf.Bool("chain.okpay.USDT.withdrawOpen", false)
	usdt := NewOkPayCoin(chain, pb.OKPAYUSDT, pb.USDT, rechargeOpen, withdrawOpen, pb.CoinConfig{
		AssetId:      int64(conf.Num[int]("chain.okpay.USDT.AssetId", 0)),
		ExchangeRate: conf.Num[float64]("chain.okpay.USDT.AssetExchangeRate", 0),
		Fees:         conf.Num[float64]("chain.okpay.USDT.Fees", 0),
		MinWithdraw:  conf.Num[float64]("chain.okpay.USDT.MinWithdraw", 0),
		MinRecharge:  conf.Num[float64]("chain.okpay.USDT.MinRecharge", 0),
	})
	coins.Reg(usdt)

	{
		rechargeOpen := conf.Bool("chain.okpay.CNY.rechargeOpen", false)
		withdrawOpen := conf.Bool("chain.okpay.CNY.withdrawOpen", false)
		cny := NewOkPayCoin(chain, pb.OKPAYCNY, pb.CNY, rechargeOpen, withdrawOpen, pb.CoinConfig{
			AssetId:      int64(conf.Num[int]("chain.okpay.CNY.AssetId", 0)),
			ExchangeRate: conf.Num[float64]("chain.okpay.CNY.AssetExchangeRate", 0),
			Fees:         conf.Num[float64]("chain.okpay.CNY.Fees", 0),
			MinWithdraw:  conf.Num[float64]("chain.okpay.CNY.MinWithdraw", 0),
			MinRecharge:  conf.Num[float64]("chain.okpay.CNY.MinRecharge", 0),
		})
		coins.Reg(cny)
	}

	chains.Reg(chain)
	return chain, nil
}

func (c *Chain) Type() pb.EnumChain {
	return pb.CHAIN_OKPAY
}

func (c *Chain) StartScan(ctx context.Context) error {
	return nil
}

func (c *Chain) Collection(coin stypes.ICoin, private string) (*big.Int, error) {
	return nil, nil
}

func (c *Chain) WithdrawOrder(amount float64, coin stypes.ICoin, address string) error {
	return nil
}

func (c *Chain) GetUserRechargeAddress(userID int64) (address string, extInfo string, err error) {
	return "", "", nil
}

func (c *Chain) GetUserRechargePrivate(userID int64) (private string, err error) {
	return "", nil
}

func (c *Chain) IsOfficialAddress(address string) bool {
	return false
}

type Response any

func NewOkayPay(id, token string) *OkPay {
	apiUrl := "https://api.okaypay.me/shop/"
	return &OkPay{
		ID:                       id,
		Token:                    token,
		ApiUrlPayLink:            apiUrl + "payLink",
		ApiUrlTransfer:           apiUrl + "transfer",
		ApiUrlTransactionHistory: apiUrl + "TransactionHistory",
	}
}

// 获取支付链接
/**
 * 获取支付链接
 *  参数
 *      unique_id : 可选(唯一编号,防止重复下单)
 *      name : 显示信息 可选
 *      amount : 金额 必须
 *      return_url : 返回链接 可选
 *      coin : [USDT,TRX] 必须
 *
 */
func (op *OkPay) PayLink(data map[string]string) (any, error) {
	op.url = op.ApiUrlPayLink
	return op.post(data)
}

// 转账给用户
/**
 * 转账给用户
 *  参数
 *      unique_id : 可选(唯一编号,防止重复下单)
 *      name : 显示信息 可选
 *      amount : 金额
 *      to_user_id : 用户tgid 用户必须启动过钱包 必须
 *      coin : [USDT,TRX] 必须
 *
 */
func (op *OkPay) Transfer(data map[string]string) (any, error) {
	op.url = op.ApiUrlTransfer
	return op.post(data)
}

// 商户帐变
func (op *OkPay) ShopTransactionHistory(data map[string]string) (any, error) {
	op.url = op.ApiUrlTransactionHistory
	return op.post(data)
}

// 异步通知
func (op *OkPay) Notify(r *http.Request) {
	var data NotifyData
	// Read POST data
	body, err := ioutil.ReadAll(r.Body)
	if err != nil {
		fmt.Println("Error reading POST data:", err)
		return
	}
	// Parse JSON into struct
	err = json.Unmarshal(body, &data)
	if err != nil {
		fmt.Println("Error unmarshalling data:", err)
		return
	}
	// Check signature
	if op.CheckSign(data) {
		fmt.Println("验证成功")
		if data.Status == "success" && data.Code == "10000" {
			// 数据正常
		} else {
			// 数据不正常
		}
	} else {
		fmt.Println("验证失败")
	}
}

// 数据签名
func (op *OkPay) Sign(data map[string]string) map[string]string {
	data["id"] = op.ID
	sortedKeys := make([]string, 0, len(data))
	for key := range data {
		if key != "sign" {
			sortedKeys = append(sortedKeys, key)
		}
	}
	// Sort keys
	sort.Strings(sortedKeys)
	var queryStrings []string
	for _, key := range sortedKeys {
		queryStrings = append(queryStrings, fmt.Sprintf("%s=%s", key, data[key]))
	}
	// Add token and calculate sign
	signature := strings.Join(queryStrings, "&") + "&token=" + op.Token
	data["sign"] = op.generateSignature(signature)
	return data
}

// 校验数据签名
func (op *OkPay) CheckSign(data NotifyData) bool {
	sign := data.Sign
	// Remove the sign before re-generating it
	data.Sign = ""
	// Create the string for signature validation
	var queryStrings []string
	if data.OrderID != "" {
		queryStrings = append(queryStrings, fmt.Sprintf("order_id=%s", data.OrderID))
	}
	if data.UniqueID != "" {
		queryStrings = append(queryStrings, fmt.Sprintf("unique_id=%s", data.UniqueID))
	}
	if data.PayUserID != "" {
		queryStrings = append(queryStrings, fmt.Sprintf("pay_user_id=%s", data.PayUserID))
	}
	if data.Amount != "" {
		queryStrings = append(queryStrings, fmt.Sprintf("amount=%s", data.Amount))
	}
	if data.Coin != "" {
		queryStrings = append(queryStrings, fmt.Sprintf("coin=%s", data.Coin))
	}
	// Sort keys and generate signature
	sort.Strings(queryStrings)
	signature := strings.Join(queryStrings, "&") + "&token=" + op.Token
	// Compare the original and generated signatures
	return sign == op.generateSignature(signature)
}

// Helper function to generate the signature
func (op *OkPay) generateSignature(data string) string {
	hash := md5.New()
	hash.Write([]byte(data))
	return strings.ToUpper(hex.EncodeToString(hash.Sum(nil)))
}

// 数据发送
func (op *OkPay) post(data map[string]string) (any, error) {
	data = op.Sign(data)
	formData := url.Values{}
	for key, value := range data {
		formData.Set(key, value)
	}
	// 创建 HTTP 请求
	client := &http.Client{Timeout: 10 * time.Second}
	req, err := http.NewRequest("POST", op.url, strings.NewReader(formData.Encode()))
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	// Read response
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	log.Warn("okpay post res:", formData.Encode(), " res:", string(body))
	// Parse JSON response
	var response any
	err = json.Unmarshal(body, &response)
	if err != nil {
		return nil, err
	}
	return response, nil
}
