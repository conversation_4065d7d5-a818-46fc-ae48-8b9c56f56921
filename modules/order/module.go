package order

import (
	"fmt"
	"s2/define"
	"s2/modules/order/chains/bsc"
	"s2/modules/order/chains/okpay"
	"s2/modules/order/chains/ton"
	"s2/modules/order/chains/tron"
	"s2/modules/order/stypes"
	"s2/modules/order/wallet"
	"s2/pb"
	"time"

	"github.com/jfcwrlight/core/basic"
	"github.com/jfcwrlight/core/conc"
	"github.com/jfcwrlight/core/conf"
	"github.com/jfcwrlight/core/iface"
	"github.com/jfcwrlight/core/infra/mdb"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/message"
	"github.com/jfcwrlight/core/system"
	"github.com/jfcwrlight/core/utils/hs"
	"golang.org/x/exp/maps"
)

// module 是 scan 模块的实现，实现了 types.IModule 接口
type module struct {
	iface.IModule
	*hs.HttpService
	chains map[pb.EnumChain]stypes.IChain // 持有 Chain 实例
}

// New 创建并返回一个新的 scan 模块实例
func New() iface.IModule {
	m := &module{
		IModule: basic.NewConcurrency(),
		chains:  map[pb.EnumChain]stypes.IChain{},
	}
	m.HttpService = hs.NewHttpService()
	return m
}

// Name 返回模块的名称
func (m module) Name() string {
	return define.ModuleName.Order
}

// Init 初始化 scan 模块，设置依赖、注册消息处理器并启动监听
func (m *module) Init() error {
	if err := m.InitScan(); err != nil {
		log.Error(err)
		return err
	}
	m.initOrder()
	m.POST("/okpay", okpayHandler)
	message.Response(m, m.onGetOrSetUserRechargeAddressReq)
	message.Response(m, m.onCheckAndCommitOrderReq)
	message.Response(m, m.onWithdrawOrderOperationReq)
	message.Response(m, m.onGetCoinMapReq)
	message.Response(m, m.onHistoryWithdrawOrderReq)
	message.Response(m, m.onHistoryRechargeOrderReq)
	message.Response(m, m.onCreateRechargeOrderReq)
	message.Response(m, m.onCreateWithdrawOrderReq)
	return nil
}

// init 初始化数据库表
func (m *module) initOrder() {
	// 使用 AutoMigrate 自动创建或迁移 recharge_orders 表
	if err := mdb.Default().AutoMigrate(&RechargeOrder{}); err != nil {
		log.Panicf("Failed to migrate recharge_orders table: %v", err)
	}
	mdb.Premigrate[RechargeOrder]("default", confirmedRechargeTableName, func() time.Time {
		// 下一月的零点
		now := time.Now().UTC()
		next := now.AddDate(0, 1, 0)
		return time.Date(next.Year(), next.Month(), 1, 0, 0, 0, 0, time.UTC)
	})
	// 使用 AutoMigrate 自动创建或迁移 withdraw_orders 表
	if err := mdb.Default().AutoMigrate(&WithdrawOrder{}); err != nil {
		log.Panicf("Failed to migrate withdraw_orders table: %v", err)
	}
	mdb.Premigrate[WithdrawOrder]("default", confirmedWithdrawTableName, func() time.Time {
		// 下一月的零点
		now := time.Now().UTC()
		next := now.AddDate(0, 1, 0)
		return time.Date(next.Year(), next.Month(), 1, 0, 0, 0, 0, time.UTC)
	})
	conc.Go(func() {
		withdrawLoop()
	})
}

// InitScan 初始化 scan 模块的核心组件，包括 wallet 和 transaction
func (m *module) InitScan() error {
	// eth, err := ethereum.New(conf.Str("chain.eth.rpcURL"))
	// if err != nil {
	// 	return err
	// }
	// m.chains[eth.Type()] = eth
	///*
	bsc, err := bsc.New()
	if err != nil {
		return err
	}
	m.chains[bsc.Type()] = bsc
	//*/

	//*
	tron, err := tron.New() // 代币的充，归集已经测试
	if err != nil {
		return err
	}
	m.chains[tron.Type()] = tron
	//*/

	// sol, err := solana.New(conf.Str("chain.solana.rpcURL"))
	// if err != nil {
	// 	return err
	// }
	// m.chains[sol.Type()] = sol

	ton, err := ton.New()
	if err != nil {
		return err
	}
	m.chains[ton.Type()] = ton

	okpay, err := okpay.New()
	if err != nil {
		return err
	}
	m.chains[okpay.Type()] = okpay

	err = wallet.InitWallet(maps.Values(m.chains))
	if err != nil {
		return err
	}
	return nil
}

func (m *module) StartScan() {
	for _, chain := range m.chains {
		conc.Go(func() {
			chain.StartScan(system.RootCtx())
		})
	}
}

func (m *module) Run() error {
	m.StartScan()
	conc.Go(func() {
		time.Sleep(time.Second * 10)
		ResendRechargeOrder()
	})
	m.IModule.Run()
	return m.ListenAndServe(fmt.Sprintf(":%d", conf.Num[int]("orderdoor.port")))
}
