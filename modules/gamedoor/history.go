package gamedoor

import (
	"encoding/json"
	"io"
	"net/http"
	"s2/common/cache"
	"s2/gsconf"
	"s2/pb"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/jfcwrlight/core/conf/table"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/message"
)

// 兼容老版本

func (m *module) initHistory() {
	group := m.Group("/verify")
	group.POST("/total", m.handleHistoryTotal)
	group.POST("/page", m.handleHistoryPage)
	group.POST("/query", m.handleHistoryQuery)
	group.POST("/spin", m.handleVerifySpin)
	group.POST("/games", m.handleGameConfigs)
}

func (m *module) handleHistoryPage(c *gin.Context) {
	b, err := io.ReadAll(c.Request.Body)
	if err != nil {
		c.String(http.StatusBadRequest, "read body failed")
		return
	}
	req := struct {
		UserID       int64 `json:"uid"`
		GameID       int32 `json:"gameid"`
		CurrencyType int32 `json:"currencyType"`
		Page         int64 `json:"page"`
		Size         int32 `json:"size"`
		Starttime    int64 `json:"starttime"`
		Endtime      int64 `json:"endtime"`
		LastSN       int32 `json:"lastsn"`
	}{}
	err = json.Unmarshal(b, &req)
	if err != nil {
		log.Errorf("req unmarshal error %s", err.Error())
		c.String(http.StatusBadRequest, "unmarshal error")
		return
	}
	bc, err := cache.QueryUserBasicInfo(req.UserID)
	if err != nil || bc == nil {
		log.Error(err)
		c.JSON(http.StatusBadRequest, nil)
		return
	}
	resp, err := message.Request[pb.GameSpinRecordResp](m.GetHistoryServerId(), &pb.GameSpinRecordReq{
		UserID:    req.UserID,
		Starttime: req.Starttime,
		Endtime:   req.Endtime,
		Limit:     req.Size,
		GameSN:    req.GameID,
		AssetID:   req.CurrencyType,
		GameID:    req.GameID,
	})
	if err != nil {
		log.Error(err)
		c.JSON(http.StatusInternalServerError, nil)
		return
	}
	c.JSON(http.StatusOK, resp.Records)
}

func (m *module) handleHistoryTotal(c *gin.Context) {
	b, err := io.ReadAll(c.Request.Body)
	if err != nil {
		c.String(http.StatusBadRequest, "read body failed")
		return
	}
	req := struct {
		UserID       int64 `json:"uid"`
		GameID       int32 `json:"gameid"`
		CurrencyType int32 `json:"currencyType"`
		Starttime    int64 `json:"starttime"`
		Endtime      int64 `json:"endtime"`
	}{}
	err = json.Unmarshal(b, &req)
	if err != nil {
		log.Errorf("req unmarshal error %s", err.Error())
		c.String(http.StatusBadRequest, "unmarshal error")
		return
	}
	bc, err := cache.QueryUserBasicInfo(req.UserID)
	if err != nil || bc == nil {
		log.Error(err)
		c.JSON(http.StatusBadRequest, nil)
		return
	}
	resp, err := message.Request[pb.GameSpinRecordCountResp](m.GetHistoryServerId(), &pb.GameSpinRecordCountReq{
		UserID:    req.UserID,
		Starttime: req.Starttime,
		Endtime:   req.Endtime,
		AssetID:   req.CurrencyType,
		GameID:    req.GameID,
	})
	if err != nil {
		log.Error(err)
		c.JSON(http.StatusInternalServerError, nil)
		return
	}
	c.String(http.StatusOK, strconv.Itoa(int(resp.Count)))
}

// 兼容老版本, 查询单条记录
func (m *module) handleHistoryQuery(c *gin.Context) {
	b, err := io.ReadAll(c.Request.Body)
	if err != nil {
		c.String(http.StatusBadRequest, "read body failed")
		return
	}
	req := struct {
		GameID int32  `json:"gameid"`
		Public string `json:"hash"`
	}{}
	err = json.Unmarshal(b, &req)
	if err != nil {
		log.Errorf("req unmarshal error %s", err.Error())
		c.String(http.StatusBadRequest, "unmarshal error")
		return
	}
	resp, err := message.Request[pb.GameSpinRecordQueryResp](m.GetHistoryServerId(), &pb.GameSpinRecordQueryReq{
		GameID: req.GameID,
		Public: req.Public,
	})
	if err != nil {
		log.Error(err)
		c.JSON(http.StatusInternalServerError, nil)
		return
	}
	c.JSON(http.StatusOK, resp.Record)
}

func (m *module) handleVerifySpin(c *gin.Context) {
	b, err := io.ReadAll(c.Request.Body)
	if err != nil {
		c.String(http.StatusBadRequest, "read body failed")
		return
	}
	req := struct {
		GameID int32  `json:"gameid"`
		Seed   string `json:"seed"`
		Mode   int32  `json:"mode"`
	}{}
	err = json.Unmarshal(b, &req)
	if err != nil {
		log.Errorf("req unmarshal error %s", err.Error())
		c.String(http.StatusBadRequest, "unmarshal error")
		return
	}
	resp, err := message.Request[pb.GameSpinPayoutResp](m.GetHistoryServerId(), &pb.GameSpinPayoutReq{
		GameID: req.GameID,
		Seed:   req.Seed,
		Mode:   req.Mode,
	})
	if err != nil {
		log.Error(err)
		c.JSON(http.StatusInternalServerError, nil)
		return
	}
	c.JSON(http.StatusOK, resp)
}

func (m *module) handleGameConfigs(c *gin.Context) {
	type item struct {
		GameID int32   `json:"gameid"`
		Name   string  `json:"name"`
		Modes  []int32 `json:"modes"`
	}
	data := map[string]*[]item{}
	list := []item{}
	allGame := table.GetALL[gsconf.GameInfoConf]()
	for _, game := range allGame {
		if game.AlgoMode[0] == -1 {
			continue
		}
		list = append(list, item{
			GameID: game.ID,
			Name:   game.Name,
			Modes:  game.AlgoMode,
		})
	}
	data["list"] = &list
	c.JSON(http.StatusOK, &data)
}
